# Api Lucree Credenciamento


Api com uma série de endpoints para consulta, listagem e cadastro da plataforma Lucree.

Uma documentação Swagger é disponibilizada com todos os endpoints.

---
## 🛢️ Stack
- Python / Django REST


---
## Requisitos de desenvolvimento e DEBUG

- [Confluence onboard e setup](https://lucree.atlassian.net/wiki/spaces/LUCREE/pages/189497345/Setup+e+onboarding)
- Docker
- VsCode
    - Configuração do arquivo launch.json para debug com VSCode.
- Renomear arquivo .env.hmlEXAMPLE na raiz do projeto para .env.hml e preencher com as  variáveis de ambiente
- Client **[pritunl](https://client.pritunl.com/#install)** para conexão VPN
- Postman


---
## 🐑 Clonando o repositório

``` bash
#ssh
<NAME_EMAIL>:bevipag/lucree-credenciamento.git
```

---
## Debug com o VsCode

Para debug com o VsCode foi disponibilizado o arquivo .vscode/launch.json que nesse caso será utilizado após build do container.

Também é necessário a configuração de um arquivo .env.hml na raiz do projeto.
Esse arquivo deve ser criado tomando como base o arquivo .env.hmlEXAMPLE que foi disponibilizado na raiz do repositório.

Para acesso ao BD RDS Aws é necessário estar conectado na VPN com o client PritUnl, esse procedimento é detalhado no Confluence em - [Confluence onboard e setup](https://lucree.atlassian.net/wiki/spaces/LUCREE/pages/189497345/Setup+e+onboarding) na seção VPN - **PritUnl Client**

O Debug pelo VsCode é feito utilizando a extensão **Docker**.

Crie o container baseado no arquivo docker-compose-dev.yml com o seguinte comando:

```
docker-compose -f docker-compose-dev.yml up -d --build
```
Esse build cria uma imagem Docker, e um container com o seguinte nome:
```
lucree-credenciamento_web
```

Navegar até a extensão **Docker** e encontrar o container em execução e clicar com o botão direito e selecionar **Attach Visual Studio Code**

A animação abaixo demonstra esse mesmo processo com outro container.
- Clicar com o botão direito na extensão docker
- Encontrar o container **lucree-credenciamento_web** e clicar com o botão direito e selecionar **Attach Visual Studio Code**
- Isso abrirá uma nova (janela) instância do VsCode, já conectada ao container que é onde faremos a Execução e debug
- Nessa nova janela do VsCode clicar **Ctrl + Shift + D** e depois **F5**


![](https://bytebucket.org/bevipag/lucree-api-tratativas-chamados/raw/dd7f1e178a2898b3055904042fa69f66040d61d8/assets/DebugExtensaoVsCode.gif?token=22eb06ed825834aeaafe3e4e769d853098e8d428)


Isso disponibilizará a aplicação em:

```
localhost:8000
```

---
## 🏭  Commit e deploy

Após implementação das alterações e testes, pode-se fazer um Pull Request para a branch **master**.
O commit ou merge na branch master dispara a pipeline e faz o deploy da função Lambda em hml na AWS.

**A última etapa é colocar o projeto em produção.**

- A pipeline de produção é disparada ao commitarmos na branch master com a tag release-*

Exemplo de tag release-2.3.21:
