
err (){
    echo "$(tput bold && tput setaf 1): $1 $(tput sgr0)"
}

main(){
    if [[ ! `jq --help` ]]; then
        err "Instale o jq: sudo apt install jq"
        return
    fi

    if [[ ! -e "$(pwd)/zappa_settings.json" ]]; then
        err "Nao existe zappa_settings.json nessa pasta"
        return
    fi

    load=`cat "$(pwd)/zappa_settings.json"`
    if [[ -z "$load" ]]; then
        err "ZappaSettings esta vazio"
        return
    fi

    envs=`echo $load | jq ."$1".environment_variables`
    for key in $( echo $envs | jq -r 'to_entries | .[] | (.key) + "=" + "\"" + (.value | tostring) + "\""' ); do
        eval "export ${key}"
    done
}

main $1
