from django.urls import path
from api import views

urlpatterns = [
    path('planos', views.Planos.as_view(), name='planos'),
    path('list_planos', views.ListPlanos.as_view(), name='list-planos'),
    path('ramo-atividade', views.Departamentos.as_view(), name='ramo-atividade'),
    path('cadastro', views.CredenciamentoAudit.as_view(), name='cadastro'),
    path('cadastro-app', views.CredenciamentoClientesApp.as_view(), name='cadastro-app'),
    path('cadastro/<int:lojista_id>', views.CredenciamentoView.as_view(), name='cadastro'),
    path('consulta/documento', views.ConsultaDocumento.as_view(), name='consulta-documento'),
    path('consulta/email', views.ConsultaEmail.as_view(), name='consulta-email'),
    path('consulta/codigo-cliente', views.ConsultaCodigoCliente.as_view(), name='consulta-codigo-cliente'),
    path('estabelecimentos-comerciais', views.Lojistas.as_view(), name='lojista'),
    # path('lojista/<int:pk>', views.Lojistas.as_view({'get': 'retrieve', 'put': 'update'}), name='lojista-detail'),
    path('endereco', views.Enderecos.as_view({'post': 'create', 'get': 'list'}), name='enderecos'),
    path('domicilio-bancario', views.DomicilioBancario.as_view({'post': 'create', 'get': 'list'}), name='domicilio-bancario'),
    path('pagamento/cartao', views.Pagamentos.as_view(), name='pagamento-cartao'),
    path('pagamento/confirmacao', views.ConfirmacaoPagamento.as_view(), name='pagamento-confirmacao'),
    path('pagamento/atualizacao/<int:lojista_id>', views.AtualizacaoPagamento.as_view(), name='pagamento-atualizacao'),
    path('pagamento/aprovacao/<int:lojista_id>', views.AprovacaoPagamento.as_view(), name='pagamento-aprovacao'),
    path('pagamento/lojista/<int:lojista_id>', views.LojistaPagamento.as_view(), name='pagamento-lojista'),
    path('pedido', views.Pedido.as_view({'post': 'create'}), name='pedido'),
    path('operadora', views.Operadora.as_view(), name='operadora'),
    path('endereco/retrieve/<int:id_lojista>', views.EnderecoLojista.as_view(), name='enderecos-lojista'),
    path('cadastro-operador', views.Agente.as_view(), name='agente'),
    path('inativacao-cadastro', views.Inativacao.as_view(), name='inativacao'),
    path('ativacao-cadastro', views.Ativacao.as_view(), name='ativacao'),
    path('responsavel', views.Responsavel.as_view({'post': 'create', 'get': 'list'}), name='responsavel'),
    path('responsavel/<int:pk>', views.Responsavel.as_view({'get': 'retrieve', 'put': 'update'}), name='responsavel-detail'),
    path('bancos', views.Bancos.as_view({'get':'list', 'post': 'create'})),
    path('get-ispb/<str:cod_banco>', views.TmpBancosView.as_view({'get':'get_unique'})),
    path('get-ispb', views.TmpBancosView.as_view({'get':'get_all'})),
    path('distribuidor', views.Distribuidores.as_view(), name='distribuidor'),
    path('backoffice', views.BackofficeUserView.as_view(), name='backoffice'),
    path('ativador-idwall', views.Idwall.as_view(), name='ativar-idwall'),
    path('consulta/status-risco', views.ConsultaPeriodica.as_view({'get': 'list'}), name='consulta-periodica'),
    path('rdstation/send', views.Rdstation.as_view({'post': 'rdstation_send'}), name='rdstation-send'),
    path('octadesk/send', views.Octadesk.as_view({'post': 'octadesk_send'}), name='octadesk-send'),
    # LUC-753
    path('cerc/send', views.CercViewSet.as_view({'post': 'cerc_send'}), name='cerc-send'),
    #SCA1609
    path('usuario-venda', views.UsuarioVenda.as_view({"get":"get", "post": "post"}), name="usuario-venda"),
    path('usuario-venda/<int:user_id>', views.UsuarioVenda.as_view({"get": "get_by_user_id", "put": "update_by_user_id"}), name="usuario-venda"),
    path('projeto/<int:projeto_id>/lojistas', views.ProjetoLojista.as_view(), name='lojistas-by-plano'),
    path('novo-usuario-venda', views.NovoUsuarioVenda.as_view(), name="novo-usuario-venda"),
    path('webhooks-credenciamento', views.Webhook.as_view(), name='webhook-credenciamento'),
    path('lojistas-por-projeto', views.PlanoLojista.as_view(), name='lojistas-e-planos-por-projeto'),
    path('novo-usuario-venda', views.NovoUsuarioVenda.as_view(), name="novo-usuario-venda"),
    path('domicilio-bancario/retrieve/<int:id_lojista>', views.DomicilioLojista.as_view({'get': 'get_unique'}), name='domicilio-lojista'),
    path('domicilio-bancario/list/<int:id_lojista>', views.DomicilioLojista.as_view({'get': 'get_many'}), name='domicilio-lojista'),
    path('domicilio-bancario/<int:id_lojista>', views.DomicilioLojista.as_view({'put': 'put'}), name='domicilio-lojista update'),
    path('domicilio-bancario/<int:id_lojista>/analise', views.DomicilioLojista.as_view({'put': 'update'}),
         name='domicilio-lojista-aux update'),
    path('domicilio-bancario/new/', views.DomicilioLojista.as_view({'post': 'new_bank'}), name='domicilio-lojista'),
    path('envia-lojas', views.DomicilioLojista.as_view({'post': 'envia_sf_whook'}), name='envia-lojas'),
    path('envia-para-sales-individual', views.DomicilioLojista.as_view({'post': 'webhook_salesforce_by_loja'}), name='envia-lojas'),
    path('altera-multi-agenda', views.MultiAgenda.as_view(), name="altera-multi-agenda"),
    path('valida-sublojista', views.Sublojista.as_view({'post': 'create'}), name='valida-sublojista'),
    path('subadiquirente/lojista/<str:documento>', views.SubadiquirenteLojistas.as_view(), name='informacoes-lojista')
]
