# Generated by Django 2.1.7 on 2019-06-13 18:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('api', '0004_auto_20190613_1802'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardBancos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cod', models.CharField(help_text='Inteiro [Código bancário]', max_length=20)),
                ('descricao', models.Char<PERSON>ield(help_text='Descrição do banco', max_length=250)),
            ],
            options={
                'db_table': 'dashboard_bancos',
            },
        )
    ]
