# Generated by Django 2.1.7 on 2019-06-13 19:44

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('api', '0005_auto_20190613_1838'),
    ]

    operations = [
        migrations.CreateModel(
            name='Auditoria',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('criador', models.Char<PERSON>ield(max_length=100)),
                ('formulario', django.contrib.postgres.fields.jsonb.JSONField()),
                ('status', models.BooleanField(default=False)),
                ('observacao', models.CharField(max_length=200)),
                ('data_criacao', models.DateTimeField(auto_now_add=True, null=True)),
                ('data_update', models.DateTime<PERSON>ield(null=True)),
            ],
            options={
                'db_table': 'auditoria_cadastro',
            },
        )
    ]
