# Generated by Django 2.1.5 on 2019-01-30 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AuthUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.Char<PERSON>ield(max_length=128)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('is_superuser', models.BooleanField()),
                ('username', models.Char<PERSON>ield(max_length=150, unique=True)),
                ('first_name', models.Char<PERSON>ield(max_length=30)),
                ('last_name', models.Char<PERSON>ield(max_length=150)),
                ('email', models.Char<PERSON>ield(max_length=254)),
                ('is_staff', models.BooleanField()),
                ('is_active', models.BooleanField()),
                ('date_joined', models.DateTimeField()),
            ],
            options={
                'db_table': 'auth_user',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardAtividadessecundaria',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('atividade', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_atividadessecundaria',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardCertidoesnegativasitens',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emitido_em', models.DateTimeField(blank=True, null=True)),
                ('fonte', models.CharField(blank=True, max_length=250, null=True)),
                ('protocolo', models.CharField(blank=True, max_length=250, null=True)),
                ('url', models.CharField(blank=True, max_length=250, null=True)),
                ('nada_consta', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_certidoesnegativasitens',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardCessao',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maquina_cessao', models.CharField(max_length=10)),
                ('foto_documento', models.CharField(max_length=400)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_cessao',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardCnpj',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cnpj', models.CharField(blank=True, max_length=16, null=True)),
                ('nome_fantasia', models.CharField(blank=True, max_length=150, null=True)),
                ('nome_empresarial', models.CharField(blank=True, max_length=150, null=True)),
                ('data_abertura', models.DateField(blank=True, null=True)),
                ('fonte', models.CharField(blank=True, max_length=50, null=True)),
                ('atividade_principal', models.CharField(blank=True, max_length=300, null=True)),
                ('faturamento_presumido', models.CharField(blank=True, max_length=300, null=True)),
                ('numero_funcionarios', models.CharField(blank=True, max_length=50, null=True)),
                ('numero_funcionarios_filiais', models.CharField(max_length=50)),
                ('regime_tributario', models.CharField(blank=True, max_length=16, null=True)),
                ('irs_status', models.CharField(blank=True, max_length=16, null=True)),
                ('capital_social', models.CharField(blank=True, max_length=16, null=True)),
                ('data_situacao_cadastral', models.DateField(blank=True, null=True)),
                ('data_situacao_especial', models.DateField(blank=True, null=True)),
                ('email', models.CharField(blank=True, max_length=250, null=True)),
                ('ente_federativo_responsavel', models.CharField(blank=True, max_length=300, null=True)),
                ('motivo_situacao_cadastral', models.CharField(blank=True, max_length=250, null=True)),
                ('natureza_juridica', models.CharField(blank=True, max_length=200, null=True)),
                ('numero', models.CharField(blank=True, max_length=16, null=True)),
                ('situacao_cadastral', models.CharField(blank=True, max_length=16, null=True)),
                ('situacao_especial', models.CharField(blank=True, max_length=50, null=True)),
                ('telefone', models.CharField(blank=True, max_length=150, null=True)),
                ('tipo', models.CharField(blank=True, max_length=150, null=True)),
                ('localizacao_bairro', models.CharField(blank=True, max_length=250, null=True)),
                ('localizacao_cep', models.CharField(blank=True, max_length=10, null=True)),
                ('localizacao_cidade', models.CharField(blank=True, max_length=50, null=True)),
                ('localizacao_complemento', models.CharField(blank=True, max_length=150, null=True)),
                ('localizacao_estado', models.CharField(blank=True, max_length=50, null=True)),
                ('localizacao_logradouro', models.CharField(blank=True, max_length=150, null=True)),
                ('localizacao_numero', models.CharField(blank=True, max_length=6, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_cnpj',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardCpf',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sexo', models.CharField(max_length=2)),
                ('numero', models.CharField(max_length=25)),
                ('data_de_nascimento', models.DateField()),
                ('nome', models.CharField(max_length=300)),
                ('renda', models.CharField(max_length=250)),
                ('pep', models.BooleanField()),
                ('situacao_imposto_de_renda', models.CharField(max_length=250)),
                ('cpf_situacao_cadastral', models.CharField(max_length=250)),
                ('cpf_data_de_inscricao', models.CharField(max_length=250)),
                ('cpf_digito_verificador', models.CharField(max_length=4)),
                ('cpf_anterior_1990', models.CharField(max_length=1)),
                ('ano_obito', models.CharField(max_length=1)),
                ('grafia', models.CharField(max_length=300)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_cpf',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardDepartamentos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo_departamento', models.IntegerField(blank=True, null=True)),
                ('descricao_departamento', models.CharField(blank=True, max_length=250, null=True)),
                ('id_mcc', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_departamentos',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardDividaativa',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=150)),
                ('valor_devido', models.CharField(max_length=20)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_dividaativa',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardDomiciliobancario',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('banco', models.CharField(help_text='String [Código do banco do Lojista (Ex: 104)]', max_length=4)),
                ('agencia', models.IntegerField(help_text='Inteiro [Agência bancária (Sem o dígito)]')),
                ('conta', models.IntegerField(help_text='Inteiro [Conta bancária]')),
                ('digito_conta', models.IntegerField(help_text='Inteiro [Dígito da conta]')),
                ('tipo_conta', models.CharField(help_text='String [CORRENTE/POUPANÇA]', max_length=10)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_domiciliobancario',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardEmailslojista',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endereco', models.CharField(max_length=250)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_emailslojista',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardEmpresas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cnpj', models.CharField(max_length=16)),
                ('nome_empresarial', models.CharField(max_length=150)),
                ('tipo_relacionamento', models.CharField(max_length=20)),
                ('cargo', models.CharField(max_length=20)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_empresas',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardEnderecolojista',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('principal', models.BooleanField(blank=True, help_text='Boolean [True/False] - Endereço principal?', null=True)),
                ('cidade', models.CharField(help_text='String [Cidade do Lojista]', max_length=50)),
                ('estado', models.CharField(help_text='String [Estado do Lojista]', max_length=2)),
                ('numero', models.CharField(help_text='String [Número do endereco do lojista] Ex: 165a, 104', max_length=10)),
                ('cep', models.CharField(help_text='String [Cep do endereço] Ex: 0000-000', max_length=9)),
                ('complemento', models.CharField(help_text='String [Complemento do endereço do lojista]', max_length=250)),
                ('logradouro', models.CharField(help_text='String [Logradouro do endereço do lojista]', max_length=250)),
                ('bairro', models.CharField(help_text='String [Bairro do endereço do lojista]', max_length=250)),
                ('tipo', models.CharField(help_text='String [Tipo do endereço do lojista] Ex: COMERCIAL/RESIDENCIAL', max_length=250)),
                ('pessoas_endereco', models.CharField(help_text='String [Quantidade de pessoas no endereço]', max_length=3)),
                ('utilizado_pedido', models.BooleanField(help_text='Boolean [Endereço utilizado na entrega?]')),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_enderecolojista',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardGrafiascnpj',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grafia', models.CharField(max_length=250)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_grafiascnpj',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardIdwall',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dashboard_idwall',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardItemvalidacoes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('regra', models.CharField(blank=True, max_length=250, null=True)),
                ('nome', models.CharField(blank=True, max_length=250, null=True)),
                ('descricao', models.CharField(blank=True, max_length=250, null=True)),
                ('resultado', models.CharField(blank=True, max_length=250, null=True)),
                ('mensagem', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_itemvalidacoes',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardLojista',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lojista_id', models.IntegerField(blank=True, null=True)),
                ('nome', models.CharField(help_text='String [Nome do lojista]', max_length=200)),
                ('documento', models.CharField(help_text='String [Nº do documento do lojista]', max_length=20, unique=True)),
                ('branch_id', models.CharField(blank=True, max_length=20, null=True)),
                ('merchant_id', models.CharField(blank=True, max_length=20, null=True)),
                ('affiliation_key', models.CharField(blank=True, max_length=250, null=True)),
                ('mensagem_idwall', models.CharField(blank=True, max_length=250, null=True)),
                ('matriz_idwall', models.CharField(blank=True, max_length=250, null=True)),
                ('numero_idwall', models.CharField(blank=True, max_length=300, null=True)),
                ('resultado_idwall', models.CharField(blank=True, max_length=250, null=True)),
                ('status_idwall', models.CharField(blank=True, max_length=20, null=True)),
                ('pep_idwall', models.BooleanField(blank=True, null=True)),
                ('fase', models.CharField(help_text='String [Fase do credenciamento] Ex: Criado via e-commerce', max_length=30)),
                ('com_protesto', models.BooleanField(blank=True, null=True)),
                ('operadora', models.CharField(blank=True, max_length=250, null=True)),
                ('departamento', models.IntegerField(help_text='Inteiro [Código do departamento comercial (MCC)] Obs: consultar no método /departamentos')),
                ('aprovacao_manual', models.BooleanField(blank=True, null=True)),
                ('email_vendedor_responsavel', models.CharField(blank=True, max_length=250, null=True)),
                ('relatorio_a_partir_pj', models.BooleanField(blank=True, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
                ('agencia_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('banco_favorecido', models.IntegerField(blank=True, null=True)),
                ('conta_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('digito_conta_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('porcentagem_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('tipo_conta_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('mensagem_integrador', models.CharField(blank=True, max_length=250, null=True)),
                ('num_terminais', models.IntegerField(help_text='String [Nº de terminais]')),
                ('observacao_liberacao_risco', models.CharField(blank=True, max_length=1040, null=True)),
                ('porcentagem_cliente', models.CharField(blank=True, max_length=250, null=True)),
                ('cnpj_favorecido', models.CharField(blank=True, max_length=250, null=True)),
                ('nome_razao_favorecido', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'db_table': 'dashboard_lojista',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardLojistaterminal',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_lojistaterminal',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardNomefonteconsulta',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=150)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_nomefonteconsulta',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardPagamento',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_pagamento', models.CharField(help_text='String [Tipo de pagamento] Ex: CRÉDITO/DÉBITO/BOLETO', max_length=25)),
                ('parcelas', models.CharField(blank=True, help_text='Inteiro [Nº de parcelas]', max_length=10, null=True)),
                ('nsu', models.CharField(blank=True, help_text='String [Código do comprovante de pagamento]', max_length=250, null=True)),
                ('data_pagamento', models.DateField(blank=True, help_text='Date [Data do pagamento]', null=True)),
                ('pagamento_valido', models.BooleanField(help_text='Boolean [Pagamento válido?]')),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_pagamento',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardParticipacaoempresasitens',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cnpj', models.CharField(blank=True, max_length=20, null=True)),
                ('nome_empresarial', models.CharField(blank=True, max_length=150, null=True)),
                ('tipo_relacionamento', models.CharField(blank=True, max_length=200, null=True)),
                ('cargo', models.CharField(blank=True, max_length=150, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_participacaoempresasitens',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardPaystore',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dashboard_paystore',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardPedido',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero_pedido', models.IntegerField(help_text='Inteiro [Nº do pedido]')),
                ('status_pedido', models.CharField(help_text='String [Status do pedido] Ex: Criado via e-commerce', max_length=250)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_pedido',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardPessoasrelacionadas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cpf', models.CharField(max_length=25)),
                ('nome', models.CharField(max_length=300)),
                ('tipo', models.CharField(max_length=20)),
                ('cargo', models.CharField(max_length=20)),
                ('cnpj', models.CharField(max_length=16)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_pessoasrelacionadas',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardPlanos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=250)),
                ('tipo_contratacao', models.CharField(max_length=250)),
                ('valor_contratacao', models.FloatField()),
                ('taxa_debito', models.FloatField()),
                ('taxa_credito', models.FloatField()),
                ('taxa_parcelado', models.FloatField()),
                ('adquirencia', models.CharField(max_length=250)),
            ],
            options={
                'db_table': 'dashboard_planos',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardProdutos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('descricao', models.CharField(max_length=250)),
                ('identificador', models.CharField(max_length=250)),
                ('gprs', models.BooleanField(blank=True, null=True)),
                ('wifi', models.BooleanField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_produtos',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardProtesto',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('estado_protesto', models.CharField(max_length=2)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_protesto',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardQsacnpj',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=250)),
                ('nome_representante', models.CharField(blank=True, max_length=250, null=True)),
                ('pais_origem', models.CharField(blank=True, max_length=250, null=True)),
                ('qualificacao', models.CharField(max_length=250)),
                ('qualificacao_representante_legal', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_qsacnpj',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardRegistration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dashboard_registration',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardResponsavel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(blank=True, max_length=250, null=True)),
                ('documento', models.CharField(blank=True, max_length=20, null=True)),
                ('tipo', models.CharField(blank=True, max_length=40, null=True)),
                ('data_nascimento', models.CharField(blank=True, max_length=40, null=True)),
                ('sexo', models.CharField(blank=True, max_length=1, null=True)),
                ('ddd', models.CharField(blank=True, max_length=2, null=True)),
                ('telefone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.CharField(blank=True, max_length=250, null=True)),
                ('nacionalidade', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'db_table': 'dashboard_responsavel',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardResumoconsultas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome_matriz', models.CharField(max_length=150)),
                ('status_protocolo', models.CharField(max_length=20)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_resumoconsultas',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardRetorno4Ward',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_type', models.IntegerField()),
                ('id_number', models.CharField(max_length=20)),
                ('codigo_adquirente', models.IntegerField()),
                ('status', models.IntegerField()),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_retorno4ward',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardSintegraitem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cnpj', models.CharField(blank=True, max_length=20, null=True)),
                ('inscricao_estadual', models.CharField(blank=True, max_length=20, null=True)),
                ('razao_social', models.CharField(blank=True, max_length=250, null=True)),
                ('cnae_principal', models.CharField(blank=True, max_length=250, null=True)),
                ('situacao_cadastral_vigente', models.CharField(blank=True, max_length=20, null=True)),
                ('data_situacao_cadastral', models.DateField(blank=True, null=True)),
                ('regime_apuracao', models.CharField(blank=True, max_length=250, null=True)),
                ('data_credenciamento_nfe', models.CharField(blank=True, max_length=250, null=True)),
                ('indicador_obrigatoriedade_nfe', models.CharField(blank=True, max_length=250, null=True)),
                ('data_obrigatoriedade_nfe', models.CharField(blank=True, max_length=250, null=True)),
                ('protocolo', models.CharField(blank=True, max_length=250, null=True)),
                ('tipo', models.CharField(blank=True, max_length=250, null=True)),
                ('uf', models.CharField(max_length=2)),
                ('cad_icms', models.CharField(blank=True, max_length=250, null=True)),
                ('cae', models.CharField(blank=True, max_length=250, null=True)),
                ('nome_fantasia', models.CharField(blank=True, max_length=250, null=True)),
                ('data_inicio_atividade', models.CharField(blank=True, max_length=250, null=True)),
                ('natureza_juridica', models.CharField(blank=True, max_length=250, null=True)),
                ('natureza_estabelecimento', models.CharField(blank=True, max_length=250, null=True)),
                ('data_abertura', models.CharField(blank=True, max_length=250, null=True)),
                ('data_baixa', models.CharField(blank=True, max_length=250, null=True)),
                ('delegacia_fazendaria', models.CharField(blank=True, max_length=250, null=True)),
                ('enquadramento_empresa', models.CharField(blank=True, max_length=250, null=True)),
                ('observacao', models.CharField(blank=True, max_length=250, null=True)),
                ('data_inscricao', models.CharField(blank=True, max_length=250, null=True)),
                ('ped', models.CharField(blank=True, max_length=250, null=True)),
                ('data_inicio_ped', models.CharField(blank=True, max_length=250, null=True)),
                ('ultrapassou_limite_estadual', models.CharField(blank=True, max_length=250, null=True)),
                ('data_inicio_simples_nacional', models.CharField(blank=True, max_length=250, null=True)),
                ('condicao', models.CharField(blank=True, max_length=250, null=True)),
                ('email', models.CharField(blank=True, max_length=250, null=True)),
                ('indicador_obrigatoriedade_efd', models.CharField(blank=True, max_length=250, null=True)),
                ('data_obrigatoriedade_efd', models.CharField(blank=True, max_length=250, null=True)),
                ('opcao_simples', models.CharField(blank=True, max_length=250, null=True)),
                ('indicador_obrigatoriedade_cte', models.CharField(blank=True, max_length=250, null=True)),
                ('data_obrigatoriedade_cte', models.CharField(blank=True, max_length=250, null=True)),
                ('situacao_sintegra', models.CharField(blank=True, max_length=250, null=True)),
                ('tipo_unidade_auxiliar', models.CharField(blank=True, max_length=250, null=True)),
                ('regime_pagamento', models.CharField(blank=True, max_length=250, null=True)),
                ('situacao_contribuinte', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_sintegraitem',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardSintegraitematividadesecundaria',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo', models.CharField(blank=True, max_length=50, null=True)),
                ('descricao', models.CharField(blank=True, max_length=150, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_sintegraitematividadesecundaria',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardSkipper',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dashboard_skipper',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardSlack',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'dashboard_slack',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardTaxas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('credito_a_vista', models.FloatField()),
                ('credito_parcelado_emissor', models.FloatField()),
                ('debito', models.FloatField()),
                ('parcelado_loja_dois_seis', models.FloatField()),
                ('parcelado_loja_sete_doze', models.FloatField()),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_taxas',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardTelefones',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fone_ddd', models.CharField(blank=True, max_length=2, null=True)),
                ('fone_numero', models.CharField(blank=True, max_length=10, null=True)),
                ('principal', models.BooleanField()),
                ('tipo_assinante', models.CharField(blank=True, max_length=20, null=True)),
                ('pessoas_com_o_mesmo_telefone', models.CharField(blank=True, max_length=5, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_telefones',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardTentativasresumoconsulta',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('duracao_tentativa', models.FloatField()),
                ('hora_fim_tentativa', models.DateTimeField(blank=True, null=True)),
                ('hora_inicio_tentativa', models.DateTimeField(blank=True, null=True)),
                ('msg_erro_tentativa', models.CharField(max_length=250)),
                ('status_fonte', models.CharField(max_length=10)),
                ('status_tentativa', models.CharField(max_length=25)),
                ('tipo_erro_tentativa', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'db_table': 'dashboard_tentativasresumoconsulta',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardTerminais',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('terminal_id_logical', models.CharField(max_length=50)),
                ('terminal_id_physical', models.CharField(max_length=50)),
                ('tecnology_code', models.IntegerField()),
                ('terminal_serial_number', models.CharField(max_length=50)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_terminais',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardTransacoes',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nsu', models.IntegerField(blank=True, null=True)),
                ('acquirer_nsu', models.CharField(blank=True, max_length=40, null=True)),
                ('value', models.FloatField(blank=True, null=True)),
                ('status', models.IntegerField(blank=True, null=True)),
                ('parcels', models.IntegerField(blank=True, null=True)),
                ('brand', models.CharField(blank=True, max_length=40, null=True)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('finish_date', models.DateTimeField(blank=True, null=True)),
                ('confirmation_date', models.DateTimeField(blank=True, null=True)),
                ('payment_date', models.DateTimeField(blank=True, null=True)),
                ('response_code', models.CharField(blank=True, max_length=10, null=True)),
                ('response_message', models.CharField(blank=True, max_length=250, null=True)),
                ('authorization_number', models.CharField(blank=True, max_length=40, null=True)),
                ('terminal', models.CharField(blank=True, max_length=40, null=True)),
                ('tef_terminal', models.CharField(blank=True, max_length=40, null=True)),
                ('terminal_serial_number', models.CharField(blank=True, max_length=40, null=True)),
                ('terminal_manufacturer', models.CharField(blank=True, max_length=40, null=True)),
                ('terminal_model', models.CharField(blank=True, max_length=40, null=True)),
                ('terminal_type', models.CharField(blank=True, max_length=40, null=True)),
                ('acquirer', models.CharField(blank=True, max_length=40, null=True)),
                ('merchant', models.CharField(blank=True, max_length=40, null=True)),
                ('tef_merchant', models.CharField(blank=True, max_length=40, null=True)),
                ('merchant_category_code', models.CharField(blank=True, max_length=40, null=True)),
                ('merchant_national_type', models.CharField(blank=True, max_length=40, null=True)),
                ('merchant_national_id', models.CharField(blank=True, max_length=40, null=True)),
                ('product_name', models.CharField(blank=True, max_length=40, null=True)),
                ('product_id', models.CharField(blank=True, max_length=40, null=True)),
                ('card_input_method', models.CharField(blank=True, max_length=40, null=True)),
                ('requested_password', models.CharField(blank=True, max_length=40, null=True)),
                ('fallback', models.CharField(blank=True, max_length=40, null=True)),
                ('origin', models.CharField(blank=True, max_length=40, null=True)),
                ('authorization_time', models.IntegerField(blank=True, null=True)),
                ('client_version', models.CharField(blank=True, max_length=250, null=True)),
                ('server_version', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'db_table': 'dashboard_transacoes',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DashboardValidacao',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mensagem', models.CharField(blank=True, max_length=250, null=True)),
                ('nome', models.CharField(blank=True, max_length=250, null=True)),
                ('resultado', models.CharField(blank=True, max_length=250, null=True)),
                ('status', models.CharField(blank=True, max_length=250, null=True)),
                ('data_cadastro', models.DateTimeField(blank=True, null=True)),
                ('data_update', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_validacao',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoContentType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_label', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
            ],
            options={
                'db_table': 'django_content_type',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoMigrations',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app', models.CharField(max_length=255)),
                ('name', models.CharField(max_length=255)),
                ('applied', models.DateTimeField()),
            ],
            options={
                'db_table': 'django_migrations',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DjangoSession',
            fields=[
                ('session_key', models.CharField(max_length=40, primary_key=True, serialize=False)),
                ('session_data', models.TextField()),
                ('expire_date', models.DateTimeField()),
            ],
            options={
                'db_table': 'django_session',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DumpCredenciamento',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_field', models.TextField(blank=True, db_column='\ufeff--', null=True)),
            ],
            options={
                'db_table': 'dump_credenciamento',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='LojistaAtivacaoterminal',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('communication_profile', models.CharField(max_length=200)),
                ('merchant', models.CharField(max_length=10)),
                ('terminal', models.CharField(max_length=15)),
            ],
            options={
                'db_table': 'lojista_ativacaoterminal',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='LojistaPaystore',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'db_table': 'lojista_paystore',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Tparcelas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nroparcela', models.IntegerField(blank=True, null=True)),
                ('nroparcela_final', models.IntegerField(blank=True, null=True)),
                ('desc_referencia', models.TextField(blank=True, null=True)),
                ('nro_incremento', models.IntegerField(blank=True, null=True)),
                ('idmodalidade', models.CharField(blank=True, max_length=40, null=True)),
            ],
            options={
                'db_table': 'tparcelas',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Ttmp',
            fields=[
                ('data', models.DateField(primary_key=True, serialize=False)),
                ('nr_dia', models.IntegerField(blank=True, null=True)),
                ('tt_dia_mes', models.IntegerField(blank=True, null=True)),
                ('nr_mes', models.IntegerField(blank=True, null=True)),
                ('nr_ano', models.IntegerField(blank=True, null=True)),
                ('dt_dia_util_anterior', models.DateField(blank=True, null=True)),
                ('dt_proximo_dia_util', models.DateField(blank=True, null=True)),
                ('fl_dia_util', models.IntegerField(blank=True, null=True)),
                ('fl_dia_util_incluindo_sabado', models.IntegerField(blank=True, null=True)),
                ('fl_feriado', models.IntegerField(blank=True, null=True)),
                ('nr_dia_semana', models.IntegerField(blank=True, null=True)),
                ('ds_dia_semana', models.TextField(blank=True, null=True)),
                ('nr_semana', models.IntegerField(blank=True, null=True)),
                ('nr_semana_mes', models.IntegerField(blank=True, null=True)),
                ('nr_dia_ano', models.IntegerField(blank=True, null=True)),
                ('qt_dias_uteis_mes', models.IntegerField(blank=True, null=True)),
                ('tt_dias_uteis_mes', models.IntegerField(blank=True, null=True)),
                ('qt_dias_uteis_ano', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'ttmp',
                'managed': False,
            },
        ),
    ]
