# Generated by Django 2.1.7 on 2019-06-13 17:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('api', '0002_auto_20190130_1440'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardBancos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cod', models.IntegerField(help_text='Inteiro [Código bancário]')),
                ('descricao', models.CharField(help_text='Descrição do banco', max_length=250)),
            ],
            options={
                'db_table': 'dashboard_bancos',
            },
        )
    ]
