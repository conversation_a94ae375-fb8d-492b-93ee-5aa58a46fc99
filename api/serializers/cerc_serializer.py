from rest_framework import serializers

from api.models import DashboardLojista


class LojistIdSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)

    def validate(self, attrs):
        """
        Serializer para validar o ID do lojista e verificar sua existência e fase de integração.

        Campos:
            id (int): ID do lojista, obrigatório.

        Validações:
            - O ID não pode ser nulo.
            - O ID deve ser um inteiro, não uma string.
            - O lojista com o ID fornecido deve existir no banco de dados.
            - O lojista deve estar em uma das fases permitidas: 
            'NÃO TRANSACIONANDO', 'TRANSACIONANDO', 'FATURAMENTO', 'COMERCIAL'.

        Levanta:
            serializers.ValidationError: Caso alguma das validações falhe.
        """
        id_lojista = attrs['id']

        if id_lojista is None:
            raise serializers.ValidationError('ID do lojista não pode ser nulo')
        if isinstance(id_lojista, str):
            raise serializers.ValidationError('ID do lojista deve ser um inteiro')

        try:
            lojista = DashboardLojista.objects.get(pk=id_lojista)
        except DashboardLojista.DoesNotExist:
            raise serializers.ValidationError('Lojista não encontrado')

        FASES_PERMITIDAS = {
            'NÃO TRANSACIONANDO',
            'TRANSACIONANDO',
            'FATURAMENTO',
            'COMERCIAL'
        }

        if lojista.fase_integracao not in FASES_PERMITIDAS:
            raise serializers.ValidationError(
                f'O lojista não está em uma fase válida. Fases permitidas: {", ".join(FASES_PERMITIDAS)}'
            )
        return attrs
