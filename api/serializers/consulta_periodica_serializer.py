import logging
from rest_framework import serializers
from api.models.models import DashboardLojista, DashboardTaxas
from .cnpj_serializer import CnpjSerializer
from .endereco_serializer import EnderecoSerializer
from datetime import datetime
from ..helpers.idwall.idwall import IdWall

logger = logging.getLogger(__name__)


class ConsultaPeriodicaSerializer(serializers.ModelSerializer):

    # def create(self, validated_data):
    #     lojista = DashboardLojista.objects.create(**validated_data)
    #     taxa = DashboardTaxas()
    #     taxa.credito_a_vista = validated_data['plano'].taxa_credito
    #     taxa.credito_parcelado_emissor = validated_data['plano'].taxa_parcelado
    #     taxa.debito = validated_data['plano'].taxa_debito
    #     taxa.parcelado_loja_dois_seis = validated_data['plano'].taxa_parcelado
    #     taxa.parcelado_loja_sete_doze = validated_data['plano'].taxa_parcelado
    #     taxa.lojista = lojista
    #     taxa.data_cadastro = datetime.now()
    #     taxa.save()
    #
    #     lojist = DashboardLojista.objects.get(id=lojista.id)
    #
    #     lojist.produto = 'Lucree Auto'
    #     lojist.email_vendedor_responsavel = validated_data['email_vendedor_responsavel']
    #     lojist.fase_integracao = 'IDWALL'
    #     lojist.banco_favorecido = 0
    #     result = IdWall.create_relatorio(lojist.matriz_idwall, lojist.documento)
    #     logger.debug(result)
    #     if result['status_code'] == 400:
    #         raise serializers.ValidationError(result['message'])
    #
    #     lojist.numero_idwall = result['result']['numero']
    #     lojist.origem = 'api-credenciamento'
    #     lojist.save()
    #
    #     return lojista

    cnpj = CnpjSerializer(read_only=True, many=True)
    endereco = EnderecoSerializer(read_only=True, many=True)
    class Meta:
        model = DashboardLojista
        fields = (
            'id',
            'nome',
            'documento',
            'produto',
            'status_idwall',
            'data_cadastro',
            'matriz_idwall',
            'cnpj',
            'endereco'
        )
