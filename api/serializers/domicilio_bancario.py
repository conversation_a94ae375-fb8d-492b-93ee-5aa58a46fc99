from rest_framework import serializers
from api.models.models import DashboardDomiciliobancario
from api.helpers.domicilio_bancario.aux_dom_bancario import get_index_tipo_documento, get_conta_terceiro


class DomicilioBancarioSerializer(serializers.ModelSerializer):
    tipo_documento = serializers.SerializerMethodField("get_documento_tipo")
    flag_conta_terceiro = serializers.SerializerMethodField("get_conta_terceiro")

    class Meta:
        model = DashboardDomiciliobancario
        fields = (
            'id',
            'banco',
            'agencia',
            'conta',
            'digito_conta',
            'tipo_conta',
            'lojista',
            'ativo',
            'documento_titular',
            'nome_titular',
            'tipo_documento',
            'matriz_titular',
            'flag_conta_terceiro'
        )

    def get_documento_tipo(self, obj):
        return get_index_tipo_documento(obj.matriz_titular)

    def get_conta_terceiro(self, obj):
        conta = get_conta_terceiro(obj.lojista.documento)
        return conta
