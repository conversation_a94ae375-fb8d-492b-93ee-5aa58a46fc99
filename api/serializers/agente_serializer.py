from rest_framework import serializers
from django.contrib.auth.models import User
from api.helpers.hierarquia_lucree.chamadas_metodos_hierarquia import Hierarquia<PERSON><PERSON>ree
from datetime import datetime
from credenciamento import settings


class AgenteSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="E-mail do operador")
        password = serializers.CharField(max_length=20, allow_blank=False, help_text="Senha do operador")
        first_name = serializers.CharField(max_length=60, allow_blank=False, help_text="Primeiro nome do operador")
        last_name= serializers.Char<PERSON>ield(max_length=60, allow_blank=False, help_text="Sobrenome do operador")
        possuiPai = serializers.BooleanField(help_text="Indica se o operador possui um pai dentro da hierarquia")
        emailPai = serializers.CharField(
            max_length=20, allow_null=True, required=False, help_text="E-mail do pai dentro da hierarquia ()Obrigatório somente caso 'Possui pai = True'")

        def create(self, validated_data):
                checkusername = User.objects.filter(username=validated_data['email']).exists()
                checkemail = User.objects.filter(email=validated_data['email']).exists()

                if checkusername or checkemail:
                        user = User.objects.get(email=validated_data['email'])

                        hierarquia = HierarquiaLucree()
                        hierarquia.get_item_hierarquico(user.id)

                else:
                        user = User.objects.create_user(
                                username=validated_data['email'],
                                password=validated_data['mudar@123'],
                                first_name=validated_data['first_name'],
                                last_name=validated_data['last_name'],
                                is_active=True,
                                date_joined=datetime.now(),
                                is_superuser=False,
                                is_staff=False
                        )
                        user.save()
                return user

        # @staticmethod
        # def validaPai(emailPai):
        #         if(User.objects.get())
