from ..serializers import DomicilioBancarioSerializer
from rest_framework import viewsets
from api.models.models import DashboardDomiciliobancario


class DomicilioBancario(viewsets.ModelViewSet):
    """
    create:
    Cadastro de domicilio bancário.

    list:
    Lista todos os domicilios bancários.
    """

    queryset = DashboardDomiciliobancario.objects.all()
    serializer_class = DomicilioBancarioSerializer
