from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.models.models import DashboardOperadoraGprs


class Operadora(APIView):
    """
    get:
    Metodo para obter as operadoras DISPONÍVEIS
    """

    @action(detail=False, methods=['get'])
    def get(self, request):
        operadoras = DashboardOperadoraGprs.objects.all()

        list = []
        for oper in operadoras:
                dict_operadora = {
                    'id': oper.id,
                    'nome': oper.nome,
                    'identificador': oper.identificador,
                    'provedor': oper.provedor
                }
                list.append(dict_operadora)
        return Response(list, status=status.HTTP_200_OK)
