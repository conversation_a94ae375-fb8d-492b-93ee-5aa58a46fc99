from ..serializers import LojistaSerializer
from rest_framework import viewsets, generics
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST, HTTP_200_OK, HTTP_404_NOT_FOUND
from api.models.models import DashboardLojista, DashboardProjetos, DashboardOperadoraGprs
from api.models.ChaveIntegracao import ChaveIntegracao
from api.serializers.lojista_serializer import LojistaSerializer
import math

class Lojistas(generics.GenericAPIView):

    queryset = DashboardLojista.objects.all()
    serializer_class = LojistaSerializer

    def get(self, request):
        if 'chave-projeto' not in request.query_params or request.query_params['chave-projeto'] == '':
            return Response(data={'status': 'failed', 'motive': 'Bad Request'}, status=HTTP_400_BAD_REQUEST)
        try:
            itens = int (request.query_params.get("itens", 10))
            pagina = int (request.query_params.get("pagina", 1))
            chave = ChaveIntegracao.objects.get(chave=request.query_params['chave-projeto'])
            projeto = chave.projeto.nome
            lojas = DashboardLojista.objects.filter(produto=projeto)[itens*(pagina-1):itens*pagina]
            count = DashboardLojista.objects.filter(produto=projeto).count()


            lojista_serializer = LojistaSerializer(lojas, many=True)
            return Response(data={"data":lojista_serializer.data,"paginas":int (math.ceil(count/itens))}, status=HTTP_200_OK)
        except ChaveIntegracao.DoesNotExist:
            return Response(data={'status': 'failed', 'motive': 'Key not found'}, status=HTTP_404_NOT_FOUND)
