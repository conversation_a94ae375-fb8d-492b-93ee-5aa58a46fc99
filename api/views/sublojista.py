from django.shortcuts import render
from rest_framework.decorators import action
from rest_framework.views import APIView
from ..serializers import ResponsavelSerializer
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from django.contrib.auth.models import User
from api.models.models import DashboardResponsavel, DashboardLojista, DashboardProfile, DashboardSublojista
from ..serializers.octadesk_serializer import LojistIdSerializer


class Sublojista(viewsets.ModelViewSet):
    serializer_class = LojistIdSerializer

    """
    create:
    Cadastro de responsavel.

    list:
    Lista todos os responsavel.

    retrieve:
    Retorna um responsavel

    update:
    Altera um responsável
    """


    def create(self, request):

        data = request.data

        lojista = DashboardLojista.objects.get(documento=data["documento"])

        sub_lojista = DashboardSublojista.objects.get(lojista_id=lojista.id, email=data["email"])


        return Response(data={"result": True}, status=status.HTTP_200_OK)

