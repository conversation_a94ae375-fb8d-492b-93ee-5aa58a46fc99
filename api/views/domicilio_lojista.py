import json
import logging
from datetime import datetime

import requests
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from api.helpers.domicilio_bancario.aux_dom_bancario import (
    atualiza_dom_bancario,
    cadastra_novo_dom_bancario, atualiza_dom_bancario_aux,
)
from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido
from api.helpers.octadesk.send_webhook import post_octadesk_webhook
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.models.models import AuthUser, DashboardDomiciliobancario, DashboardLojista
from api.views.webhook import Webhook

from ..helpers.custom_exceptions.cerc import CercException
from ..helpers.telegram import Telegram
from ..serializers import DomicilioBancarioSerializer

logger = logging.getLogger(__name__)


class DomicilioLojista(ModelViewSet):
    """
    get:
    Metodo para obter domicilio bancário por id do lojista

    get_many:
    Metodo para obter domicilio bancário por id do lojista

    new_bank:
    Metodo para obter domicilio bancário por id do lojista
    """

    serializer_class = DomicilioBancarioSerializer

    def get_unique(self, request, id_lojista):
        try:
            lojista = DashboardLojista.objects.get(id=id_lojista)
            domicilio = DashboardDomiciliobancario.objects.get(
                lojista=lojista, ativo=True
            )
            serializer = DomicilioBancarioSerializer(domicilio)

            return Response(serializer.data, status=status.HTTP_200_OK)
        except DashboardDomiciliobancario.MultipleObjectsReturned:
            return Response(
                {"error": "Esse lojista possui mais de uma conta bancária cadastrada"},
                status=status.HTTP_300_MULTIPLE_CHOICES,
            )
        except DashboardDomiciliobancario.DoesNotExist:
            return Response(
                {"error": "Esse domicilio não existe"}, status=status.HTTP_404_NOT_FOUND
            )

    @staticmethod
    def set_responsible_user(integration: bool = False, wich: str = None, user_id=None):
        if integration:
            responsible_user = wich
            local = wich
        else:
            responsible = AuthUser.objects.get(id=user_id)
            responsible_user = str(responsible.username)
            local = "SGC"
        return responsible_user, local

    def put(self, request, id_lojista):
        try:
            data = request.data
            data["lojista_id"] = id_lojista

            lojista = DashboardLojista.objects.get(id=id_lojista)

            data["conta_terceiro"] = data["documento_titular"] != lojista.documento

            responsible_user, local = self.set_responsible_user(
                integration=data["integration"] if "integration" in data else False,
                wich=data["wich"] if "wich" in data else None,
                user_id=data["user_id"] if "user_id" in data else None,
            )

            atualiza_dom_bancario(data, responsible_user, lojista, local)

            message = f"""Olá galera! Atualização de dados bancários. \n\nFeita por: {responsible_user}\n\nsituação: {'Ativo' if data['ativo'] else 'Inativo'}\n\nO Cliente {lojista.nome} de documento {lojista.documento} do projeto {lojista.produto}\nacabou de atualizar seu domicílio bancário com os seguintes dados:\n\nBANCO: {data["banco"]}\nAGÊNCIA: {data["agencia"]}\nCONTA: {data["conta"]}\nDÍGITO DA CONTA: {data["digito_conta"]}\nTIPO DA CONTA: {data["tipo_conta"]}\nDOCUMENTO TITULAR: {data["documento_titular"]}\nNOME TITULAR: {data["nome_titular"]}\nCONTA DE TERCEIRO: {'SIM' if data["conta_terceiro"] else 'NÃO'}\n
            """
            Telegram(
                token="**********************************************",
                chat_id="-536934004",
            ).send_message(message=message)

            return Response(
                {"response": "Gravado com sucesso"}, status=status.HTTP_200_OK
            )
        except (AttributeError, CercException, Exception) as e:
            error = f"Erro ao Gravar {str(e)}"
            logger.error(error)
            return Response({"error": error}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, id_lojista):
        try:
            data = request.data
            data["lojista_id"] = id_lojista

            lojista = DashboardLojista.objects.get(id=id_lojista)

            data["conta_terceiro"] = data["documento_titular"] != lojista.documento

            responsible_user, local = self.set_responsible_user(
                integration=data["integration"] if "integration" in data else False,
                wich=data["wich"] if "wich" in data else None,
                user_id=data["user_id"] if "user_id" in data else None,
            )

            if data["conta_terceiro"] :
                atualiza_dom_bancario_aux(data, responsible_user, lojista, local)
                message = f"""Olá galera! Solicitação Atualização de dados bancários para Aprovação. \n\nFeita por: {responsible_user}\n\nsituação: {'Ativo' if data['ativo'] else 'Inativo'}\n\nO Cliente {lojista.nome} de documento {lojista.documento} do projeto {lojista.produto}\nacabou de solicitar atualização de seu domicílio bancário com os seguintes dados:\n\nBANCO: {data["banco"]}\nAGÊNCIA: {data["agencia"]}\nCONTA: {data["conta"]}\nDÍGITO DA CONTA: {data["digito_conta"]}\nTIPO DA CONTA: {data["tipo_conta"]}\nDOCUMENTO TITULAR: {data["documento_titular"]}\nNOME TITULAR: {data["nome_titular"]}\nCONTA DE TERCEIRO: {'SIM' if data["conta_terceiro"] else 'NÃO'}\n
                """
            else:
                atualiza_dom_bancario(data, responsible_user, lojista, local)
                message = f"""Olá galera! Atualização de dados bancários. \n\nFeita por: {responsible_user}\n\nsituação: {'Ativo' if data['ativo'] else 'Inativo'}\n\nO Cliente {lojista.nome} de documento {lojista.documento} do projeto {lojista.produto}\nacabou de atualizar seu domicílio bancário com os seguintes dados:\n\nBANCO: {data["banco"]}\nAGÊNCIA: {data["agencia"]}\nCONTA: {data["conta"]}\nDÍGITO DA CONTA: {data["digito_conta"]}\nTIPO DA CONTA: {data["tipo_conta"]}\nDOCUMENTO TITULAR: {data["documento_titular"]}\nNOME TITULAR: {data["nome_titular"]}\nCONTA DE TERCEIRO: {'SIM' if data["conta_terceiro"] else 'NÃO'}\n
                """

            Telegram(
                token="**********************************************",
                chat_id="-536934004",
            ).send_message(message=message)

            return Response(
                {"response": "Gravado com sucesso"}, status=status.HTTP_200_OK
            )
        except (AttributeError, CercException, Exception) as e:
            error = f"Erro ao Gravar {str(e)}"
            logger.error(error)
            return Response({"error": error}, status=status.HTTP_400_BAD_REQUEST)


    def get_many(self, request, id_lojista):
        try:
            lojista = DashboardLojista.objects.get(id=id_lojista)
            domicilios = DashboardDomiciliobancario.objects.filter(
                lojista=lojista
            ).order_by("-ativo")

            serializer = DomicilioBancarioSerializer(domicilios, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except DashboardLojista.DoesNotExist:
            return Response(
                {"error": "Lojista não encontrado"}, status=status.HTTP_404_NOT_FOUND
            )
        except DashboardDomiciliobancario.DoesNotExist:
            return Response(
                {"error": "Esse lojista não possui domicilio bancário cadastrado"},
                status=status.HTTP_404_NOT_FOUND,
            )

    def new_bank(self, request):
        # data = request.data
        # lojista = DashboardLojista.objects.get(id=data['lojista'])
        #
        # responsible_user, local = self.set_responsible_user(
        #     integration=data['integration'] if 'integration' in data else False,
        #     wich=data['wich'] if 'wich' in data else None,
        #     user_id=data['user_id'] if 'user_id' in data else None
        # )
        #
        #
        # message = f"""Olá galera! Atualização de dados bancários.\n\n Por: {responsible_user}\n\n O Cliente {lojista.nome} de documento {lojista.documento} do projeto {lojista.produto}\nacabou de adicionar um domicílio bancário com os seguintes dados:\n\nBANCO: {data["banco"]}\nAGÊNCIA: {data["agencia"]}\nCONTA: {data["conta"]}\nTIPO DA CONTA: {data["tipo_conta"]}\nDOCUMENTO TITULAR: {data["documento_titular"]}\nNOME TITULAR: {data["nome_titular"]}\n
        # """
        #
        # Telegram(
        #     token='**********************************************',
        #     chat_id='-536934004'
        # ).send_message(
        #     message=message
        # )
        try:
            #     # cadastra_novo_dom_bancario(data, responsible_user, lojista, local)
            #     data['lojista_id'] = data['lojista']
            #     atualiza_dom_bancario(data, responsible_user, lojista, local)
            return Response(
                {"response": "Domicilio salvo com sucesso!"},
                status=status.HTTP_201_CREATED,
            )
        except (AttributeError, Exception) as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def envia_sf_whook(self, request):
        try:
            domicilios = (
                DashboardDomiciliobancario.objects.select_related("lojista")
                .filter(lojista_id__lt=1573, ativo=True)
                .exclude(lojista__nome="")
                .order_by("-id")
            )
            for dom in domicilios:
                print("Preparando o envio...")
                print(dom.lojista.id)
                # print(loja.id)
                post_rdstation_webhook(dom.lojista.id)
                post_octadesk_webhook(dom.lojista.id)
            return Response(
                {"response": "Cadastros enviados"}, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def webhook_salesforce_by_loja(self, request):
        merchant_id = request.data.get("merchant_id", None)
        try:
            if merchant_id:
                domicilio = (
                    DashboardDomiciliobancario.objects.select_related("lojista")
                    .filter(lojista_id=merchant_id, ativo=True)
                    .exclude(lojista__nome="")
                    .order_by("-id")
                    .first()
                )
                post_rdstation_webhook(domicilio.lojista.id)
                post_octadesk_webhook(domicilio.lojista.id)
                msg = "Enviado para o Salesforce"
            else:
                msg = "Lojista não informado"
                return Response({"msg": msg}, status=status.HTTP_400_BAD_REQUEST)
            return Response({"msg": msg}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"msg": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
