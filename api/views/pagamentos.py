import logging
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from api.serializers.pagamento import PagamentoSerializer
from api.helpers.functions.funcs import Funcs
from api.helpers.post_processing.credenciamento_processing import pagamento_put_process, pagamento_aprovacao_put_process
from api.models import *
from rest_framework.views import APIView
import json

logger = logging.getLogger(__name__)


class Pagamentos(APIView):
    """
    get:
    Método para coletar pagamentos realizados
    """

    serializer_class = PagamentoSerializer

    @action(detail=False, methods=['get'])
    def get(self, request):
        try:
            pagamentos = DashboardPagamento.objects.filter(pagamento_valido=False)
            data = []

            for c in pagamentos:
                data.append({
                    'id': c.id,
                    'id_cliente': c.lojista_id,
                    'cod_autorizacao': c.cod_autorizacao,
                    'nsu': c.nsu,
                    'valor': c.valor,
                    'data_transacao': c.data_pagamento
                })
        except Exception as e:
            logger.debug(e)
            return Response({"msg":"Falha ao capturar pagamentos"}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            data=data,
            status=status.HTTP_200_OK)


class ConfirmacaoPagamento(APIView):
    def post(self, request):
        data = request.data
        logger.info('Payload received: {}'.format(data))
        PROJETOS_CONFIRMA_PAGTO = ['QUEROPAY', 'LUCREE CENTRAL DA PECA']

        c_cancelados = DashboardLojista.objects.filter(pk__in=[ obj['id'] for obj in data['cancelados'] ] )
        for c in c_cancelados:
            sub_fase = Funcs.fase_atual(c.fase)
            if c.fase_integracao == 'PAGAMENTO' and sub_fase == 'Pagamento em confirmação':
                logger.info('Fase antiga: {}'.format(c.fase))

                c = self.stage_validate(c, [sub_fase], 'Pagamento não confirmado')
                logger.info('Fase nova: {}'.format(c.fase))
                c.save()

        ids = [ obj['id'] for obj in data['confirmados'] ]
        c_confirmados = DashboardLojista.objects.filter(pk__in=ids)
        c_responsaveis = DashboardResponsavel.objects.filter(lojista__in=ids)
        responsaveis = {}

        for r in c_responsaveis:
            responsaveis[r.lojista.id] = r
            
        for c in c_confirmados:
            sub_fase = Funcs.fase_atual(c.fase)
            if c.fase_integracao == 'PAGAMENTO' and sub_fase == 'Pagamento em confirmação':
                if c.produto.upper() in PROJETOS_CONFIRMA_PAGTO:
                    resp = responsaveis[c.id]
                    c = self.stage_validate(c, [sub_fase], 'Análise inicial')
                    c = self.stage_inhibition(c, ['Pagamento em confirmação', 'Pagamento não confirmado', 'Pagamento pendente vendedor'])
                    c = Funcs.send_idwall(c, resp.data_nascimento)
                else:
                    c = self.stage_validate(c, [sub_fase], 'Análise Id Wall')
                    c = self.stage_inhibition(c, ['Pagamento não confirmado'])

                c.fase_integracao = 'IDWALL'
                c.save()

        txn_ids = [ pg['id_txn'] for pg in data['confirmados']]
        txn_ids_invalids = [ pg['id_txn'] for pg in data['cancelados']]
        DashboardPagamento.objects.filter(id__in=txn_ids).update(pagamento_valido=True)
        DashboardPagamento.objects.filter(id__in=txn_ids_invalids).update(pagamento_valido=False)

        return Response(
            status=status.HTTP_204_NO_CONTENT
        )

    def stage_validate(self, client, stages_completed, next_stage):
        client.fase = Funcs.atualiza_fase(
            fases_list=json.loads(client.fase),
            fases_concluidas=stages_completed,
            proxima_fase=next_stage
        )
        return client

    def stage_inhibition(self, client, stages_inhibited):
        client.fase = Funcs.desabilita_fase(
            fases_list=client.fase,
            fases_desabilitar=stages_inhibited
        )
        return client


class AtualizacaoPagamento(APIView):
    '''
    put:
        json
        {
                "lojista": {
                "dados_pagamento": {
                        "cod_autorizacao": [string] Código de autorização da transação,
                        "valor": [float] Valor da venda ,
                        "data_pagamento": [string] Data da transação,
                        "nsu": [string] Nsu da transação,
                        "comprovante": [string] arquivo comprovante
                    }
                }
            }
    '''

    @action(detail=False, methods=['put'])
    def put(self, request, lojista_id):

        try:
            lojista = DashboardLojista.objects.get(id=lojista_id)
            pagamento_put_process(request.data, lojista)
            return Response({'response': 'Pagamento atualizado com sucesso'}, status=status.HTTP_200_OK)

        except (ValueError, AttributeError):
            error = 'Erro ao atualizar o Pagamento'
            logger.error(error)
            return Response({'error': error}, status=status.HTTP_400_BAD_REQUEST)


class AprovacaoPagamento(APIView):
    '''
    put:
        json
        {}
    '''

    @action(detail=False, methods=['put'])
    def put(self, request, lojista_id):

        try:
            lojista = DashboardLojista.objects.get(id=lojista_id)
            pagamento_aprovacao_put_process(lojista)
            return Response({'response': 'Pagamento aprovado com sucesso'}, status=status.HTTP_200_OK)

        except (ValueError, AttributeError):
            error = 'Erro ao aprovar o Pagamento'
            logger.error(error)
            return Response({'error': error}, status=status.HTTP_400_BAD_REQUEST)


class LojistaPagamento(APIView):
    @action(detail=False, methods=['get'])
    def get(self, request, lojista_id):

        try:

            pagamentos = DashboardPagamento.objects.filter(pagamento_valido=False, lojista_id=lojista_id).values()
            if len(pagamentos) == 0 :
                return Response( { "msg" : "Pagamento não existe" } , status=status.HTTP_404_NOT_FOUND)

            return Response( pagamentos[0] , status=status.HTTP_200_OK)

        except (ValueError, AttributeError):
            error = 'Erro ao atualizar o Pagamento'
            logger.error(error)
            return Response({'error': error}, status=status.HTTP_400_BAD_REQUEST)
