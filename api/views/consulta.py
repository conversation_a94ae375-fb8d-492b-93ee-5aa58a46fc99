from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from api.helpers.consulta import lojista_exist, user_exist, codigo_cliente_exist


class ConsultaDocumento(APIView):

    def post(self, request):
        try:
            documento = request.data['documento']
        except KeyError:
            return Response({'error': "Chave 'documento' nao encontrada no payload"}, status=status.HTTP_400_BAD_REQUEST)

        if lojista_exist(documento):
            return Response({'error': 'Documento do cliente já existe'}, status=status.HTTP_409_CONFLICT)
        return Response({'response': 'Ok'}, status=status.HTTP_200_OK)


class ConsultaEmail(APIView):

    def post(self, request):
        try:
            email = request.data['email']
        except KeyError:
            return Response({'error': "Chave 'email' nao encontrada no payload"}, status=status.HTTP_400_BAD_REQUEST)

        if user_exist(email):
            return Response({'error': 'Email já existe'}, status=status.HTTP_409_CONFLICT)
        return Response({'response': 'Ok'}, status=status.HTTP_200_OK)


# LUC-801 - <PERSON><PERSON> - 22/08/2020
class ConsultaCodigoCliente(APIView):

    def post(self, request):
        try:
            codigo_cliente = request.data['codigo_cliente']
        except KeyError:
            return Response({'error': "Chave 'codigo_cliente' nao encontrada no payload"}, status=status.HTTP_400_BAD_REQUEST)

        if codigo_cliente_exist(codigo_cliente):
            return Response({'error': 'Código do cliente já existe'}, status=status.HTTP_409_CONFLICT)
        return Response({'response': 'Ok'}, status=status.HTTP_200_OK)
