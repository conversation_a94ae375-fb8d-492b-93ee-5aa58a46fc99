import logging

from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import APIView

from api.helpers.gestao_multi_agenda import GestaoMultiAgenda


class MultiAgenda(APIView):
    """
    post:
    Re<PERSON>be o webhook de informações da multi agenda para um cliente
    """

    def post(self, request):
        try:
            data = request.data
            gestao_multi_agenda = GestaoMultiAgenda()
            cliente, existe, msg = gestao_multi_agenda.busca_cliente_por_id(data['cliente_id'])
            if existe:
                gestao_multi_agenda.altera_multiagenda_cliente(cliente, data['multi_agenda'])
                return Response({
                    "message": "Flag alterada com sucesso!",
                    "status_multi_agenda": data["multi_agenda"]}, status=status.HTTP_200_OK)
            else:
                return Response({
                    "message": "Cliente não existente",
                    "status_multi_agenda": data["multi_agenda"]}, status=status.HTTP_404_NOT_FOUND)
        except KeyError as error:
            return Response({"message": "Bad request, favor verifique os campos: " + str(error)},
                            status=status.HTTP_400_BAD_REQUEST)
        except Exception as error:
            return Response({"message": "Erro inesperado! Detalhamento: " + str(error)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

