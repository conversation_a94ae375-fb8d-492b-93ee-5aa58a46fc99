from datetime import datetime
import logging
import requests
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework import viewsets
from django.contrib.auth.models import User
from django.db import transaction
from api.helpers.idwall.idwall import IdWall
from api.models import Credenciamento, DashboardLojista
from api.helpers import credenciamento_post_process, credenciamento_update, credenciamento_get
from api.helpers.cred_exceptions import UserAlreadyExist, LojistaAlreadyExist
from api.helpers.consulta import validate_form, validate_type_client_app, validate_form_app, user_exist
import json
from api.models.cargo import DashboardCargo
from os import environ

from api.models.profile import ProdutosAdquiridos, Profile

logger = logging.getLogger(__name__)


class CredenciamentoView(APIView):
    '''
    patch:
        json
        {
            "ramo-atividade": "[int] Código de atividade comercial MCC. Obs: consultar end-point /ramo-atividade",
            "num_terminais": "[int] quantidade de maquinas",
            "responsavel": {
                "ddd": "[int] 11",
                "nome": "[string] Captão américa",
                "sexo": "[string] M",
                "email": "[email] <EMAIL>",
                "telefone": "[string] 11666-9055",
                "documento": "[string] 97431534035",
                "nacionalidade": "[string] brasileiro",
                "data_nascimento": "[string] 1997-09-10"
            },
            "endereco": {
                "cep": "[string] 12345-678",
                "tipo": "[string] 1",
                "bairro": "[string] Bairro",
                "cidade": "[string] Cidade",
                "estado": "[string] SP",
                "numero": "[string] 12",
                "principal": "[boolean] true / false",
                "logradouro": "[string] Passagem Leonor Fernandes",
                "complemento": "[string] 420 bloco 3"
            }
        }
    '''
    @action(detail=False, methods=['get'])
    def get(self, request, lojista_id):
        logger.info(f'BUSCA DE CADASTRO METODO GET - ID: {lojista_id}')
        try:
            lojista = DashboardLojista.objects.get(id=lojista_id)
            return Response(credenciamento_get(lojista), 200)
        except DashboardLojista.DoesNotExist:
            return Response({"error": "lojista não encontrado"}, 400)
        except Exception as err:
            return Response({"erro": str(err)}, 500)

    @action(detail=False, methods=['patch'])
    def patch(self, request, lojista_id):
        data = request.data
        logger.info(f'ATUALIZAÇÃO DE CADASTRO PELO METODO PATCH - DADOS ENVIADOS: {json.dumps(data)}')
        try:
            lojista = DashboardLojista.objects.get(id=lojista_id)
            credenciamento_update(lojista, data)
            idwall = IdWall()
            if lojista.matriz_idwall == 'lucree_QSA_pf':
                idwall_relatorio = idwall.create_relatorio(
                    lojista.matriz_idwall, data['lojista']['responsavel']['documento'],
                    data_nascimento=data['lojista']['responsavel']['data_nascimento']
                )
            else:
                idwall_relatorio = idwall.create_relatorio(
                    lojista.matriz_idwall, data['lojista']['dados']['documento']
                )

            idwall_numero = idwall_relatorio['result']['numero']
            lojista.numero_idwall = idwall_numero
            lojista.save()
            return Response({'response': 'Cadastro realizado com sucesso'}, status=status.HTTP_200_OK)
        except (ValueError, AttributeError, Exception) as e:
            error = 'Erro ao realizar o cadastro'
            logger.error(error)
            return Response({'error': "{} : {}".format(error,  str(e))}, status=status.HTTP_400_BAD_REQUEST)


class CredenciamentoAudit(APIView):
    '''
    post:
    ``` json
    {
    "criador": "[string] Alexandre Fialho de Araújo",
    "formulario": {
        "email_vendedor_responsavel": "[string] <EMAIL>",
        "origem": "[string] Integrador ou Applicativo",
        "produto": "[string] Lucree Auto ou CredMoura ou Gateway",
        "lojista": {
            "plano": "[int] plano da loja. Obs: consultar end-point /planos",
            "arquivos": [
                {
                    "documento": "[string] https://s3.amazonaws.com/doccessaolucree/media/foto_documento.3aa68ad737128f950f1b.pdf",
                    "tipo_arquivo": "[string] foto_documento"
                },
                {
                    "documento": "[string] https://s3.amazonaws.com/doccessaolucree/media/foto_documento_adesao.70ec1bdfa761c633016b.pdf",
                    "tipo_arquivo": "[string] foto_documento_adesao"
                },
                {
                    "documento": "[string] https://s3.amazonaws.com/doccessaolucree/media/foto_documento_domicilio_bancario.1498c975e2ab979b0b5f.e6861acfa42821e10251",
                    "tipo_arquivo": "[string] foto_documento_domicilio_bancario"
                },
                {
                    "documento": "[string] https://s3.amazonaws.com/doccessaolucree/media/foto_documento.ca3080c2f32f5a9997aa.pdf",
                    "tipo_arquivo": "[string] foto_documento"
                }
            ],
            "endereco": {
                "cep": "[string] 12345-678",
                "tipo": "[string] 1",
                "bairro": "[string] Bairro",
                "cidade": "[string] Cidade",
                "estado": "[string] SP",
                "numero": "[string] 12",
                "principal": "[boolean] true / false",
                "logradouro": "[string] Passagem Leonor Fernandes",
                "complemento": "[string] 420 bloco 3"
            },
            "documento": "[string] documento da loja (CPF) ou (CNPJ)",
            "operadora": "[int] operadora do chip. Obs: consultar end-point /operadora",
            "responsavel": {
                "ddd": "[int] 11",
                "nome": "[string] Captão américa",
                "sexo": "[string] M",
                "email": "[email] <EMAIL>",
                "telefone": "[string] 11666-9055",
                "documento": "[string] 97431534035",
                "nacionalidade": "[string] brasileiro",
                "data_nascimento": "[string] 1997-09-10"
            },
            "num_terminais": "[int] quantidade de maquinas",
            "matriz_lojista": "[string] (lucree_QSA_pf) se documento da loja for CPF ou (lucree_QSA_pf) se for CNPJ",
            "ramo-atividade": "[int] Código de atividade comercial MCC. Obs: consultar end-point /ramo-atividade",
            "dados_bancarios": {
                "banco": "[string] 021",
                "conta": "[int] 167349",
                "agencia": "[string] 4545",
                "tipo_conta": "[string] Poupanca",
                "digito_conta": "[string 1"
            },
            "tipo_contratacao": "[string] COMODATO. Obs: informação se encontra no retorno do end-point /planos",
            "dados_pagamento": {
                "cod_autorizacao": [string] Código de autorização da transação,
                "valor": [float] Valor da venda ,
                "data_pagamento": [string] Data da transação,
                "nsu": [string] Nsu da transação
            }
        }
    }
}
    ```

    put:
        json
        {
            "lojista": {
            "dados_pagamento": {
                    "cod_autorizacao": [string] Código de autorização da transação,
                    "valor": [float] Valor da venda ,
                    "data_pagamento": [string] Data da transação,
                    "nsu": [string] Nsu da transação
                }
            }
        }
    '''

    @action(detail=False, methods=['post'])
    def post(self, request):
        data = request.data
        logger.debug('PAYLOAD RECEBIDO NO CREDENCIAMENTO: {}'.format(data))

        try:
            validate_form(data)
        except LojistaAlreadyExist:
            return Response({'error': 'Documento do cliente já existe'},
                            status=status.HTTP_400_BAD_REQUEST)
        except KeyError:
            return Response({'error': 'Formulario inválido'},
                            status=status.HTTP_400_BAD_REQUEST)

        credenciamento = Credenciamento.objects.create(**data)
        try:
            cred_post = credenciamento_post_process(credenciamento.id)
            cred_post.save()
            return Response({'response': 'Cadastro realizado com sucesso'}, status=status.HTTP_200_OK)
        except (ValueError, AttributeError, Exception) as e:
            error = 'Erro ao realizar o cadastro'
            logger.error(error)
            return Response({'error': "{} : {}".format(error,  str(e))}, status=status.HTTP_400_BAD_REQUEST)


class CredenciamentoClientesApp(APIView):
    '''
    post:
    ``` json
    {
        "email": "<EMAIL>",
        "nome": "Daniel",
        "sobrenome": "Lima do Nascimento",
        "produtos_contratados": {"wallet": false, "credits": false, "withdraw": false, "insurance": false, "acquirance": true},
        "documento": "12343254650",
        "telefone": "11976549293",
        "password": "asenhaquequiser"
    }
}
    ```
    '''

    @action(detail=False, methods=['post'] )
    def post(self, request):

        data = request.data
        logger.debug('NOVO CLIENTE DO APP: {}'.format(data))
        try:
            validate_form_app(data['email'], data['documento'])
        except UserAlreadyExist:
            """
            para não interferir na função user_exist, coloquei a seguinte regra de negócio aqui:
            Quando o cliente for abrir uma conta pf e já tiver uma conta pj (mesmo email e PJ), precisa concatenar o numero do cpf no email anterior
            """
            check_merchant_type_results = ProdutosAdquiridos.objects.filter(email__iendswith=data['email'])
            if len(check_merchant_type_results) > 1:
                return Response({'error': 'Esse cliente já possui cadastro nas duas modalidades: PF/PJ'},
                            status=status.HTTP_400_BAD_REQUEST)
            elif len(check_merchant_type_results):
                if len(check_merchant_type_results[0].documento) == 14: #é cnpj
                    data['email'] = f"{data['documento']}+{data['email']}"
                    return self.insert_app_user(data)
                return Response({'error': 'Email do cliente já existe, por favor, utilize outro e-mail'},
                            status=status.HTTP_400_BAD_REQUEST)
            else:
                return self.insert_app_user(data)
        except LojistaAlreadyExist:
            return Response({'error': 'Documento do cliente já existe'},
                            status=status.HTTP_400_BAD_REQUEST)
        except KeyError:
            return Response({'error': 'Formulario inválido'},
                            status=status.HTTP_400_BAD_REQUEST)

        return self.insert_app_user(data)
    

    def insert_app_user(self, data):
        try:
            with transaction.atomic():
                prod_search = ProdutosAdquiridos.objects.filter(documento=data['documento']).first()
                if prod_search:
                    loja = DashboardLojista.objects.filter(documento=data['documento']).first()
                    if loja:
                        prod_search.cliente = loja
                    else:
                        prod_search.cliente = None
                    prod_search.save()
                    return Response({'error': 'Documento do cliente já existe, por favor, utilize outro documento'},
                            status=status.HTTP_400_BAD_REQUEST) 
                else:
                    if not user_exist(data['email']):
                        user = User.objects.create_user(
                            password=data['password'],
                            is_superuser=False,
                            is_staff=True,
                            email=data['email'],
                            username=data['email'],
                            first_name=str(data['nome']).upper(),
                            last_name=str(data['sobrenome']).upper(),
                            is_active=True,
                            last_login=datetime.now()
                        )
                        Profile.objects.create(
                            user=user,
                            tipo_perfil='Cliente',
                            tipo_projeto='App Lucree',
                            tipo_cargo=DashboardCargo.objects.get(pk=8),
                            projeto_id=45
                        )
                    
                    ProdutosAdquiridos.objects.create(
                        cliente=None,
                        produtos_contratados=data['produtos_contratados'],
                        documento=data['documento'],
                        email=data['email'],
                        nome=f"{data['nome']} {data['sobrenome']}".upper(),
                        tipo=validate_type_client_app(data['documento']),
                        telefone=data['telefone']
                    )
                try:
                    agreementsData : dict = { 
                        "type": ["2"],
                        "device_plataform": data['device_plataform'],
                        "app_version": data['app_version'],
                        "customer_ip": data['customer_ip'],
                        "document_number": data['documento']
                        }
                    url: str = environ.get("API_AGREEMENTS")
                    response = requests.post(url, json=agreementsData)
                    print(response)
                    logging.debug('Termos Aceitados: ', response.status_code)
                except Exception as err: 
                    logging.error(err)
                logger.debug('Cadastro realizado com sucesso dentro do app: documento {}/email {}/ telefone {}')
                return Response({'response': 'Cadastro realizado com sucesso'}, status=status.HTTP_200_OK)
        except (ValueError, AttributeError, Exception) as e:
            error = 'Erro ao realizar o cadastro'
            logger.error(error)
            return Response({'error': "{} : {}".format(error,  str(e))}, status=status.HTTP_400_BAD_REQUEST)