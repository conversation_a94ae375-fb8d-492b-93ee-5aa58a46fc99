import logging
import math

from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from api.models.models import TmpBancos
from api.serializers.tmp_bancos_serializer import TmpBancosSerializer

logger = logging.getLogger(__name__)


class TmpBancosView(ModelViewSet):
    """
    get:
    Metodo para obter ispb de um determinado banco

    """
    queryset = TmpBancos.objects.all()
    serializer_class = TmpBancosSerializer

    def get_unique(self, request, cod_banco):
        try:
            tmp_bancos = TmpBancos.objects.get(cod=cod_banco)
            serializer = TmpBancosSerializer(tmp_bancos)

            return Response({"data": serializer.data}, status=status.HTTP_200_OK)
        except TmpBancos.MultipleObjectsReturned:
            return Response(
                {"error": "Esse código de banco retorna mais de um banco."},
                status=status.HTTP_300_MULTIPLE_CHOICES,
            )
        except TmpBancos.DoesNotExist:
            return Response(
                {"error": "Não existe banco cadastrado com esse código"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception:
            return Response(
                {"error": "Erro inesperado."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_all(self, request):
        try:
            items = int(request.query_params.get("items", 20))
            page = int(request.query_params.get("page", 1))
            tmp_bancos = self.queryset[items*(page-1):items*page]
            count = TmpBancos.objects.all().count()
            serializer = TmpBancosSerializer(tmp_bancos, many=True)

            return Response({"data": serializer.data, "pages": int(math.ceil(count/items))}, status=status.HTTP_200_OK)
        except TmpBancos.MultipleObjectsReturned:
            return Response(
                {"error": "Esse código de banco retorna mais de um banco."},
                status=status.HTTP_300_MULTIPLE_CHOICES,
            )
        except TmpBancos.DoesNotExist:
            return Response(
                {"error": "Não existe banco cadastrado com esse código"}, status=status.HTTP_404_NOT_FOUND
            )