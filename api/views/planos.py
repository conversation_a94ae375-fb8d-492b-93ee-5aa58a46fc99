import logging
from django.db.models import Q
from rest_framework.generics import ListAPIView, GenericAPIView
from api.models.models import DashboardPlanos, DashboardProjetos
from ..serializers import PlanosSerializer
from rest_framework.decorators import action

from rest_framework import status
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST, HTTP_200_OK, HTTP_404_NOT_FOUND
from api.models.models import DashboardPlanos, DashboardProjetos
from api.models.ChaveIntegracao import ChaveIntegracao
from api.serializers.planos_serializer import PlanosSerializer

logger = logging.getLogger(__name__)


class Planos(GenericAPIView):
    """
    list:
    Metodo para obter os planos.
    """
    queryset = DashboardPlanos.objects.all()
    serializer_class = PlanosSerializer

    def get(self, request):
        if 'chave-projeto' not in request.query_params or request.query_params['chave-projeto'] == '':
            return Response(data={'status': 'failed', 'motive': 'Bad Request'}, status=HTTP_400_BAD_REQUEST)
        try:
            chave = ChaveIntegracao.objects.get(chave=request.query_params['chave-projeto'])
            projeto = chave.projeto.nome
            plano = DashboardPlanos.objects.filter(tipo_projeto=projeto)

            lojista_serializer = PlanosSerializer(plano, many=True)
            return Response(data=lojista_serializer.data, status=HTTP_200_OK)
        except ChaveIntegracao.DoesNotExist:
            return Response(data={'status': 'failed', 'motive': 'Key not found'}, status=HTTP_404_NOT_FOUND)


class ListPlanos(ListAPIView):
    """
    list:
    Metodo para obter os planos de acordo com seu Cargo e Produto.
    """
    queryset = DashboardPlanos.objects.all()
    serializer_class = PlanosSerializer

    @action(detail=False, methods=['get'])
    def get(self, request):
        data = request

        try:

            produto = request.GET.get('produto')
            cargo = request.GET.get('cargo')

        except KeyError as e:
            logger.error('Chave não encontrada: {}'.format(e))
            return Response({'error': 'Payload inválido!'},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            planos = self.get_plano(cargo, produto)
            retorno = []

            for item in planos:
                retorno.append(PlanosSerializer(item).data)

            return Response(data=retorno, status=status.HTTP_200_OK)

        except ValueError as e:
            return Response({'error': e},
                            status=status.HTTP_400_BAD_REQUEST)

    def get_plano(self, cargo, produto):
        planos = []

        if produto != 'CredMoura': #Lucree Auto/QueroPay/Lucree Outros
            projeto = DashboardProjetos.objects.get(pk=produto)
            queryset = DashboardPlanos.objects.filter(
                projeto=projeto,
                final_vigencia=None,
                cargos__ids__contains=[int(cargo)]
            )

            for item in queryset:
                planos.append(item)

        else: # Para não quebrar a integração Propig em quanto eles não colocam o integrador deles no ar
            queryset = DashboardPlanos.objects.filter(
                Q(tipo_projeto=produto) &
                Q(final_vigencia=None) &
                Q(tipo_contratacao='CESSAO'))

            for item in queryset:
                planos.append(item)
        return planos
