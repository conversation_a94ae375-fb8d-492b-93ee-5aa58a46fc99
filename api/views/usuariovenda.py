import logging
from logging import log

from rest_framework.views import APIView
from api.models.models import <PERSON>th<PERSON><PERSON>, DashboardLojista, UsuarioVenda as UsuVenda
from rest_framework.decorators import action
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from django.db import connection
from rest_framework import status
from api.helpers.post_processing.credenciamento_processing import build_user, build_profile, build_hierarquia
from api.helpers.functions.funcs import parse_name
from django.contrib.auth.models import Permission
from api.serializers import UsuarioVendaSerializer

logger = logging.getLogger(__name__)


def raw_to_dict(conn):
    columns = [col[0] for col in conn.description]
    return [
        dict(zip(columns, row))
        for row in conn.fetchall()
    ]


# LUC-753
class UsuarioVenda(ModelViewSet):
    """
    get:
    Pega os lojista de acordo com os planos

    post:
    cria o vendedor lojista
    """
    serializer_class = UsuarioVendaSerializer

    def get(self, request):

        with connection.cursor() as conn:

            conn.execute("""
                SELECT A.user_id as "user_id", A.lojista_id as "lojista_id", (concat(C.first_name,' ',C.last_name)) as "nome", C.email as "email", D.documento as "documento", D.nome as "lojista"  FROM credenciamento.dashboard_usuariovenda A
                LEFT JOIN credenciamento.auth_user C ON c.id = A.user_id
                left join credenciamento.dashboard_lojista D on D.id = A.lojista_id
            """)
            users = raw_to_dict(conn) or []
        return Response({"data": users}, status=200)

    def get_by_user_id(self, request, user_id):
        try:
            with connection.cursor() as conn:
                conn.execute(f"""
                    SELECT A.user_id as "user_id", A.lojista_id as "lojista_id",(concat(C.first_name,' ',C.last_name)) as "nome", C.email as "email", D.documento as "documento", D.nome as "lojista"  FROM credenciamento.dashboard_usuariovenda A
                        LEFT JOIN credenciamento.auth_user C ON c.id = A.user_id
                        left join credenciamento.dashboard_lojista D on D.id = A.lojista_id
                    where A.user_id={user_id}
                """)
                users = raw_to_dict(conn) or []
            if users:
                return Response({"data": users[0]}, status=200)
        except Exception as error:
            logging.error(error)
        return Response({"error": "usuario não encontrado"}, status=403)

    def update_by_user_id(self, request, user_id):
        try:
            data = request.data
        except Exception as error:
            logging.error(error)

        try:
            user = UsuVenda.objects.get(user_id=user_id)
            auth = AuthUser.objects.get(id=user_id)
        except (AuthUser.DoesNotExist, UsuVenda.DoesNtExist) as error:
            return Response({"error": "usuario não encontrado"}, status=400)
        except Exception as error:
            logging.error(error)
            return Response({"error": "usuario não encontrado"}, status=400)

        # converte o nome completo para first_name and last_name
        name: str = data.get("nome", auth.first_name + " " + auth.last_name)
        first_name, last_name = parse_name(name)

        try:
            user.lojista_id = data.get("lojista_id", user.lojista_id)
            auth.first_name = first_name
            auth.last_name = last_name
            user.save()
            auth.save()
        except Exception as error:
            logging.error(error)
            return Response({"error": "lojista não encontrado"}, status=400)

        return Response({"message": "atualizado os campos"}, status=200)

    def post(self, request):
        try:
            data = request.data
            user_id = data["user_id"]
            lojista_id = data["lojista_id"]
        except Exception as error:
            logging.error(error)
            return Response({"error": "payload invalido"}, status=400)

        try:
            UsuVenda(user_id=user_id, lojista_id=lojista_id).save()
        except Exception as error:
            logger.error(error)
            return Response({"error": "usuario já existe na tabela"}, status=409)
        return Response({"message": "usuario registrado"}, status=201)


class NovoUsuarioVenda(APIView):
    """
    post:
    Cadastra um novo usuário de venda
    """

    def post(self, request):
        try:
            data = request.data
            nome = data["nome"]
            email = data["email"]
            lojista_id = data["lojista_id"]
        except Exception as error:
            logging.error(error)
            return Response({"error": "payload invalido"}, status=400)

        try:
            # UsuVenda(user_id=user_id, lojista_id=lojista_id).save()

            form = {
                "lojista": {
                    "responsavel": {
                        "nome": nome,
                        "email": email
                    }
                },
                "produto": "Lucree Auto"
            }

            # Criando usuário
            user = build_user(form)
            user.save()

            # Adicionando permissões do usuário lojista Lucree
            # conforme tarefa SDESK-618 do Sprint 12
            permission_view_lojista = Permission.objects.filter(codename='view_lojista').first()
            user.user_permissions.add(permission_view_lojista)
            user.save()

            # criando profile
            profile = build_profile(
                form,
                user.id,
                "Lucree Auto",
                ""
            )
            profile.save()

            # ajustando hierarquia
            lojista = DashboardLojista.objects.filter(id=lojista_id).first()
            if lojista:
                hierarquia = build_hierarquia(form, user, lojista)
                hierarquia.save()

                # cadastrando na tabela de usuário de venda
                UsuVenda(user_id=user.id, lojista_id=lojista.id).save()

        except Exception as error:
            logger.error(error)
            return Response({"error": "usuario já existe na tabela" + str(error)}, status=409)
        return Response({"message": "usuario registrado"}, status=201)
