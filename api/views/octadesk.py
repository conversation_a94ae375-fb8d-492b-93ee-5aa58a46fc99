from rest_framework import status
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from api.helpers.octadesk.send_webhook import post_octadesk_webhook
from api.serializers.octadesk_serializer import LojistIdSerializer


class Octadesk(viewsets.ModelViewSet):
    serializer_class = LojistIdSerializer

    """
    post:
    Método para enviar informações para o Octadesk
    """

    @action(detail=False, methods=['post'])
    def octadesk_send(self, request):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            send_octadest = post_octadesk_webhook(serializer.validated_data['id'])
            if send_octadest.status_code in [500, 400]:
                return Response({'message': send_octadest.data['message']}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                return Response({'message': 'Registro realizado!'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'message': e}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
