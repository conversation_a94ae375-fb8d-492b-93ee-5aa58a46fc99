import logging
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import status
from api.serializers.agente_serializer import AgenteSerializer
from django.contrib.auth.models import User
from api.helpers.hierarquia_lucree.chamadas_metodos_hierarquia import Hierar<PERSON><PERSON><PERSON><PERSON><PERSON>
from api.models import *

logger = logging.getLogger(__name__)


class Agente(generics.GenericAPIView):
    """
    post:
    Metodo para cadastrar o operador
    """
    serializer_class = AgenteSerializer
    
    @action(detail=False, methods=['post'])
    def post(self, request):
        checkusername = User.objects.filter(username=request.data['email']).exists()
        checkemail = User.objects.filter(email=request.data['email']).exists()

        if checkusername or checkemail:
            user = User.objects.get(email=request.data['email'])
            return Response("Usuário já cadastrado", status=status.HTTP_200_OK)

        else:
            email_user = request.data['email']
            password_user = request.data['password']
            first_name_user = request.data['first_name']
            last_name_user = request.data['last_name']
            possuiPai_user = request.data['possuiPai']
            logger.debug(possuiPai_user)
            # Verifica se email do pai foi preenchido
            if possuiPai_user == True :
                email_pai_user = request.data['emailPai']
                user_pai = User.objects.get(email=email_pai_user)
                id_pai = user_pai.id

            else:
                email_pai_user = ""
                id_pai = ""
        try:
            user = User.objects.create_user(
                username=email_user,
                password=password_user,
                email=email_user,
                first_name=first_name_user,
                last_name=last_name_user,
                is_active=True,
                date_joined=datetime.now(),
                is_superuser=False,
                is_staff=False
            )
            user.save()
            logger.debug(user.id)

        except Exception as e:
            logger.debug(e)
            return False

        profile = DashboardProfile()
        profile.distribuidor = ''
        profile.tipo_perfil = "operador"
        profile.user = user

        profile.save()

        hierarquia = HierarquiaLucree()
        user_cadastrado = User.objects.get(email=email_user)

        dict_h = {
            'usu_nome': user_cadastrado.first_name + ' ' + user_cadastrado.last_name,
            'usu_parent': id_pai,
            'data_cadastro': datetime.now(),
            'data_update': datetime.now(),
            'cargo': 7,
            'id_usu': user_cadastrado.id,
            'produto': 1
            }
        hierarquia.cadastra_item_hierarquia(dict_h)

        return Response("Usuário cadastrado com sucesso", status=status.HTTP_200_OK)
