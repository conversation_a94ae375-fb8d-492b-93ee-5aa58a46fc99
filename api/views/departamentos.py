from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.models.models import DashboardDepartamentos


class Departamentos(APIView):
    """
    get:
    Metodo para obter os Ramos de atividade aceitos pelo Integrador
    """

    @action(detail=False, methods=['get'])
    def get(self, request):
        documento = request.query_params.get('documento')
        """Verifica se o valor documento foi passado como parâmetro e se é um CPF. Caso seja, aplica o filtro de MCCs"""
        if documento and documento.__len__() == 11:
            lista_mcc_pessoa_fisica=[742, 1799, 4121, 5697, 5963, 7230, 7299, 7538, 8011, 8021, 8099, 8111, 8999, 7399, 7379, 7349, 5970, 5699, 5499, 5399]
            departamentos = DashboardDepartamentos.objects.filter(id_mcc__in=lista_mcc_pessoa_fisica)
        else:
            departamentos = DashboardDepartamentos.objects.all()
        list = []
        for dep in departamentos:
            dict_dep = {
                'cod': dep.codigo_departamento,
                'descricao': dep.descricao_departamento
            }
            list.append(dict_dep)
        return Response(list, status=status.HTTP_200_OK)
