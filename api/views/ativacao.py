import logging
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import status
from api.serializers.ativacao_serializer import AtivacaoSerializer
from django.contrib.auth.models import User
from api.helpers.hierarquia_lucree.chamadas_metodos_hierarquia import Hierar<PERSON><PERSON><PERSON><PERSON><PERSON>
from api.models import *

logger = logging.getLogger(__name__)


class Ativacao(generics.GenericAPIView):
    """
    post:
    Metodo para ativar ou desativar um cadastro
    """

    serializer_class = AtivacaoSerializer

    @action(detail=False, methods=['post'])
    def post(self, request):
        try:
            checkloja = DashboardLojista.objects.filter(documento=request.data['documento']).exists()

            if checkloja:
                loja = DashboardLojista.objects.get(documento=request.data['documento'])
                loja.cadastro_ativo = True
                loja.save()
                if loja.lojista_id == 0:
                    pass
                else:
                    user = User.objects.get(id=loja.lojista_id)
                    user.is_active = True
                    user.save()

        except Exception as e:
            logger.debug(e)
            return Response({"msg":"Falha ao ativar o cliente"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"msg":"Cliente ativado com sucesso"}, status=status.HTTP_200_OK)
