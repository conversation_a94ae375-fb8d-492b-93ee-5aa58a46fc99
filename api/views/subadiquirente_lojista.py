from collections import defaultdict
from rest_framework import generics
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_404_NOT_FOUND
from api.serializers.endereco_serializer import EnderecoSerializer
from api.serializers.departamento_serializer import DepartamentoSerializer
from api.models.models import DashboardDepartamentos, DashboardEnderecolojista, DashboardLojista

class SubadiquirenteLojistas(generics.GenericAPIView):
    serializer_class = DepartamentoSerializer

    def get(self, request, documento: str):
        try:
            lojista = DashboardLojista.objects.get(documento=documento)
            endereco = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo=1).first()
            departamento = DashboardDepartamentos.objects.get(id=lojista.departamento)
        except DashboardLojista.DoesNotExist:
            return Response(data={'message': 'Lojista não encontrado.'}, status=HTTP_404_NOT_FOUND)
        except DashboardEnderecolojista.DoesNotExist:
            return Response(data={'message': 'Endereço do lojista não encontrado.'}, status=HTTP_404_NOT_FOUND)
        except DashboardDepartamentos.DoesNotExist:
            return Response(data={'message': 'Departamento do lojista não encontrado.'}, status=HTTP_404_NOT_FOUND)

        endereco_serialized = EnderecoSerializer(endereco).data
        departamento_serialized = self.serializer_class(departamento).data
        city = endereco_serialized.get('cidade')
        response = defaultdict(
            mcc = departamento_serialized.get('id_mcc', None),
            address = endereco_serialized.get('logradouro', None),
            city = city.replace("'", "") if city is not None else city,
            state = endereco_serialized.get('estado', None),
            country = 'BR',
            zip_code = endereco_serialized.get('cep', None)
        )

        return Response(data=response, status=HTTP_200_OK)
