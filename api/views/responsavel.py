from django.shortcuts import render
from rest_framework.decorators import action
from rest_framework.views import APIView
from ..serializers import ResponsavelSerializer
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from django.contrib.auth.models import User
from api.models.models import DashboardResponsavel, DashboardLojista, DashboardProfile



class Responsavel(viewsets.ModelViewSet):
    """
    create:
    Cadastro de responsavel.

    list:
    Lista todos os responsavel.

    retrieve:
    Retorna um responsavel

    update:
    Altera um responsável
    """

    queryset = DashboardResponsavel.objects.all()
    serializer_class = ResponsavelSerializer

    @transaction.atomic
    def create(self, request):

        data = request.data

        try:
            
            responsavel = DashboardResponsavel()

            responsavel.nome = data['nome']
            responsavel.documento = data['documento']
            responsavel.tipo = data['tipo']
            responsavel.data_nascimento = data['data_nascimento']
            responsavel.sexo = data['sexo']
            responsavel.ddd = data['ddd']
            responsavel.telefone = data['telefone']
            responsavel.email = data['email']
            responsavel.nacionalidade = data['nacionalidade']
            responsavel.lojista = DashboardLojista.objects.get(id=data['lojista'])
        
        except: 
            
            return Response(data={ 'message': 'requisição inválida' }, status=status.HTTP_400_BAD_REQUEST)

        try:
            
            responsavel.save()

            user = User.objects.create_user(
                username=responsavel.email,
                email=responsavel.email,
                password='mudar123')

            user.save()

            profile = DashboardProfile()

            profile.user = user
            profile.distribuidor = ''
            profile.tipo_perfil = 'operador'

            profile.save()
        
        except:

            return Response(data={ 'message': 'ocorreu um erro no servidor' }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        
        return Response(data=ResponsavelSerializer(responsavel).data, status=status.HTTP_201_CREATED)

    