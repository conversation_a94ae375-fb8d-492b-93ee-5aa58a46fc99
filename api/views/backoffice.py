from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from api.models import AuthUser, DashboardCargo, Profile
from django.contrib.auth.models import User, Group

class BackofficeUserView(APIView):
    def get(self, request):

        # groups = [
        #     { 'name': 'Administração' },
        #     { 'name': 'Atendimento' },
        #     { 'name': 'Cadastro' },
        #     { 'name': 'Faturamento' },
        #     { 'name': 'Financeiro' },
        #     { 'name': 'Logística' },
        #     { 'name': '<PERSON><PERSON><PERSON>' }
        # ]

        #email = request.data['email']
        email = request.GET.get('email', '')

        user = User.objects.filter(email=email).first()

        if user is not None:
            #profile = Profile.objects.filter(user=user).first()
            #if profile is not None:
            #    if profile.tipo_perfil == '' and profile.tipo_projeto == '' and profile.tipo_cargo.id == 9:
            groups = []
            for g in user.groups.all():
                groups.append({ 'name': g.name })

            return Response(data={ 'groups': groups }, status=status.HTTP_200_OK)

        return Response(data={}, status=status.HTTP_404_NOT_FOUND)
