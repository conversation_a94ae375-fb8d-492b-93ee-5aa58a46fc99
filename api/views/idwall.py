import logging
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import status
from api.serializers.ativacao_idwall_serializer import AtivacaoIdWallSerializer
from django.contrib.auth.models import User
from api.helpers.hierarquia_lucree.chamadas_metodos_hierarquia import HierarquiaLucree
from api.models import *
from rest_framework.views import APIView
from api.helpers.pendentes import send_pendente
from api.helpers.idwall.idwall import IdWall
from api.helpers.functions.funcs import Funcs

logger = logging.getLogger(__name__)


class Idwall(generics.GenericAPIView):
    """
    post:
    Metodo para ativar o fluxo da idwall
    """

    serializer_class = AtivacaoIdWallSerializer

    @action(detail=False, methods=['post'])
    def post(self, request):
        data = request.data
        try:
            logger.info('Payload received: {}'.format(data))
            idwall = IdWall()
            lojista = DashboardLojista.objects.get(documento=data['documento'])


            if lojista.matriz_idwall == 'lucree_QSA_pf':
                idwall_relatorio = idwall.create_relatorio(
                    lojista.matriz_idwall, data['documento'], data_nascimento=data['cpf_data_nascimento'])
                    
            else:
                idwall_relatorio = idwall.create_relatorio(
                    lojista.matriz_idwall, data['documento'])
                    
            idwall_numero = idwall_relatorio['result']['numero']

                
            lojista.numero_idwall = idwall_numero
            lojista.save()
            return Response(
                status=status.HTTP_204_NO_CONTENT
            )
        except KeyError as e:
            return Response(
                data={'msg': 'Dado inválido na criacao de relatorio IDWall: {}'.format(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

        except DashboardLojista.DoesNotExist:
            return Response(
                data={'msg': 'Lojista não encontrado'},
                status=status.HTTP_404_NOT_FOUND
            )

