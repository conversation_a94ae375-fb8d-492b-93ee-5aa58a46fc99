from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from api.models.models import DashboardLojista, DashboardProjetos

# LUC-753
class ProjetoLojista(APIView):
    """
    get:
    Pega os lojista de acordo com os planos
    """
    @action(detail=False, methods=['get'])
    def get(self, request, projeto_id):
        try:
            # pega o projeto refente ao id do projeto
            projeto = DashboardProjetos.objects.get(id=projeto_id)
        except:
            return Response({"error": "projeto não encontrado"}, status=203)
        
        # pega todos os lojista de acordo com o projeto
        lojistas = DashboardLojista.objects.values("id", "nome", "documento").filter(produto=projeto.nome)
        return Response({"data": lojistas}, status=200)