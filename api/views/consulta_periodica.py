from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework import status
from api.models.models import DashboardDomiciliobancario, DashboardLojista
from ..serializers import ConsultaPeriodicaSerializer


class ConsultaPeriodica(GenericViewSet):
    serializer_class = ConsultaPeriodicaSerializer
    """
    list:
    Metodo para obter lista de lojistas para a consulta periodica
    """
    @action(detail=False, methods=['get'])
    def list(self, request):
        queryset = DashboardLojista.objects.filter()
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        paginated = self.get_paginated_response(serializer.data)
        return paginated
