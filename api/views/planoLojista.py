from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from api.models.models import DashboardLojista, DashboardProjetos

class PlanoLojista(APIView):
    """
    get:
    Pega os lojistas e seus planos de acordo com os projeto
    """
    @action(detail=False, methods=['get'])
    def get(self, request, projeto_id):
        try:
            # pega o projeto refente ao id do projeto
            projeto = DashboardProjetos.objects.get(id=projeto_id)
        except:
            return Response({"error": "projeto não encontrado"}, status=404)

        # pega todos os lojista de acordo com o projeto
        lojistas = DashboardLojista.objects.select_related('plano').select_related('plano_virtual').filter(produto=projeto.nome)
        return Response({"data": lojistas}, status=200)
