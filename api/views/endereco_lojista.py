from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from api.models.models import DashboardEnderecolojista, DashboardLojista
from ..serializers import EnderecoSerializer


class EnderecoLojista(APIView):
    """
    get:
    Metodo para obter endereço por id do lojista
    """
    @action(detail=False, methods=['get'])
    def get(self, request, id_lojista):
        lojista = DashboardLojista.objects.get(id=id_lojista)
        endereco = DashboardEnderecolojista.objects.get(lojista=lojista, tipo='1')
        serializer = EnderecoSerializer(endereco)
        return Response(serializer.data, status=status.HTTP_200_OK)




