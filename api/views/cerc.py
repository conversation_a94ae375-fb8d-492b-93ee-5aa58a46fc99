from rest_framework import viewsets, response, status, serializers
from rest_framework.decorators import action

from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido
from api.serializers.cerc_serializer import LojistIdSerializer
from credenciamento.log_service import Logger

logger = Logger()


class CercViewSet(viewsets.ModelViewSet):
    serializer_class = LojistIdSerializer

    """
    post:
    Método para enviar informações para a CERC
    """

    @action(detail=False, methods=['post'])
    def cerc_send(self, request):
        try:
            serializer = LojistIdSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            if not serializer.is_valid():
                return response.Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            lojista_id = serializer.data.get('id')
            """Recebe como INT e converte para String"""
            enviado, retorno = envio_cerc_engine_sem_metodo_definido(str(lojista_id))
            if not enviado:
                return  response.Response(retorno, status=status.HTTP_400_BAD_REQUEST)
            logger.registrar(f"Lojista enviado com sucesso para CERC", "INFO")
            return response.Response({"info": "Lojista enviado com sucesso para CERC"}, status=status.HTTP_200_OK)
        except serializers.ValidationError as e:
            logger.registrar(f"Erro ao enviar lojista para a CERC: {e}", "ERROR")
            return response.Response({str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.registrar(f"Erro ao enviar lojista para a CERC: {e}", "CRITICAL")
            return response.Response({str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
