# Create your views here.

from api.models.models import DashboardBancos
from ..serializers import BancosSerializer
from rest_framework import viewsets
from rest_framework.pagination import PageNumberPagination

class CustomPagination(PageNumberPagination):
    page_size = 400
    page_size_query_param = "page_size"
    max_page_size = 10000

class Bancos(viewsets.ModelViewSet):

    """
    create:
    Cadastro de Bancos.

    list:
    Lista todos os Bancos.
    """
    pagination_class = CustomPagination
    queryset = DashboardBancos.objects.all()
    serializer_class = BancosSerializer