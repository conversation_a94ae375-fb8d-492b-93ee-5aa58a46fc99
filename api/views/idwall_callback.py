import logging
from rest_framework.views import APIView
from rest_framework.decorators import action
from api.helpers.idwall.callback import idwall_callback

logger = logging.getLogger(__name__)


class IdwallCallback(APIView):

    @action(detail=False, methods=['post'])
    def post(self, request):

        data = request.data

        try:
            # Salva o webhook enviado
            pass
        except Exception as e: 
            pass

        finally:
            idwall_callback(data)
