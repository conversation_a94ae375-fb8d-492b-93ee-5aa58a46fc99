from rest_framework.decorators import action
from rest_framework import viewsets
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.serializers.octadesk_serializer import LojistIdSerializer


class Rdstation(viewsets.ModelViewSet):
    serializer_class = LojistIdSerializer

    """
    post:
    Método para enviar informações para o RD Station
    """
    @action(detail=False, methods=['post'])
    def rdstation_send(self, request):
        lojista_id = request.data['id']
        response = post_rdstation_webhook(id=lojista_id)
        return response
