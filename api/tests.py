import logging
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIRequestFactory, APIClient
from .models import DashboardDepartamentos
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)


class EntryModelTest(TestCase):

    def test_auth(self):
        url = reverse('autenticacao')
        u = User.objects.create_user(username='user', email='<EMAIL>', password='pass')
        u.is_active = False
        u.save()

        d = DashboardDepartamentos()
        d.codigo_departamento = 1
        d.descricao_departamento = 'dEPARTAMENTO TESTE'
        d.id_mcc = 2
        d.save()


        resp = self.client.post(url, {'email': 'user', 'password': 'pass'}, format='json')
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

        u.is_active = True
        u.save()

        resp = self.client.post(url, {'username': 'user', 'password': 'pass'}, format='json')
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertTrue('token' in resp.data)
        token = resp.data['token']
        logging.debug(token)

        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION='JWT ' + 'abc')
        resp = client.get('/api/v1/departamentos', data={'format': 'json'})
        self.assertEqual(resp.status_code, status.HTTP_401_UNAUTHORIZED)
        client.credentials(HTTP_AUTHORIZATION='JWT ' + token)
        resp = client.get('/api/v1/departamentos', data={'format': 'json'})
        self.assertEqual(resp.status_code, status.HTTP_200_OK)