import os
import csv
from datetime import datetime
from pprint import pprint
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "credenciamento.settings")
django.setup()
from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido

CHUNK_SIZE = 500
MAX_WORKERS = 20


def send_or_put_massive(lista_para_envio: List[str]) -> List[dict]:
    """
    O CERC Engine tem uma característica particular,
    muito embora espere o ID do lojista, ele não aceita o tipo
    INT mas sim o STR.
    """
    retornos = []
    for lojista_id in lista_para_envio:
        resultado = {"lojistas_id": lojista_id}
        try:
            enviado, retorno = envio_cerc_engine_sem_metodo_definido(lojista_id)
            if enviado:
                resultado["status"] = "Enviado"
                resultado["retorno"] = retorno
                retornos.append(resultado)
                print(f"Lojista de id {lojista_id} enviado para a CERC")
            else:
                resultado["status"] = "Erro"
                resultado["retorno"] = retorno
                retornos.append(resultado)
                print(f"Erro ao enviar lojista de id {lojista_id}: {retorno}")
        except Exception as e:
            print(f"Erro ao enviar lojista de id {lojista_id}: {e}")
    return retornos


def send_or_put_massive_v2(lista_para_envio: List[str]) -> List[dict]:
    """
    O CERC Engine tem uma característica particular,
    muito embora espere o ID do lojista, ele não aceita o tipo
    INT mas sim o STR
    """
    retornos = []
    lista_str = [str(lojista_id) for lojista_id in lista_para_envio]

    for lojista_id in lista_str:
        resultado = {"lojistas_id": lojista_id}
        try:
            enviado, retorno = envio_cerc_engine_sem_metodo_definido(
                lojista_id)
            if enviado:
                resultado["status"] = "Enviado"
                resultado["retorno"] = retorno
                retornos.append(resultado)
                print(f"Lojista de id {lojista_id} enviado para a CERC")
            else:
                resultado["status"] = "Erro"
                resultado["retorno"] = retorno
                retornos.append(resultado)
                print(f"Erro ao enviar lojista de id {lojista_id}: {retorno}")
        except Exception as e:
            print(f"Erro ao enviar lojista de id {lojista_id}: {e}")
    pprint(retornos)


def envio_worker(lojista_id: str) -> Dict:
    resultado = {"lojistas_id": lojista_id}
    try:
        enviado, retorno = envio_cerc_engine_sem_metodo_definido(lojista_id)
        if enviado:
            resultado["status"] = "Enviado"
            resultado["retorno"] = retorno
            print(f"Lojista de id {lojista_id} enviado para a CERC")
        else:
            resultado["status"] = "Erro"
            resultado["retorno"] = retorno
            print(f"Erro ao enviar lojista de id {lojista_id}: {retorno}")
    except Exception as e:
        resultado["status"] = "Exception"
        resultado["retorno"] = str(e)
        print(f"Erro ao enviar lojista de id {lojista_id}: {e}")
    return resultado


def chunkify(lista: List[str], tamanho: int) -> List[List[str]]:
    return [lista[i:i + tamanho] for i in range(0, len(lista), tamanho)]


def send_or_put_massive_v2(lista_para_envio: List[str]) -> List[Dict]:
    retornos = []
    lista_str = [str(lojista_id) for lojista_id in lista_para_envio]
    chunks = chunkify(lista_str, CHUNK_SIZE)

    for i, chunk in enumerate(chunks):
        print(f"\nProcessando chunk {i + 1}/{len(chunks)} com {len(chunk)} itens")

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_id = {executor.submit(envio_worker, lojista_id): lojista_id for lojista_id in chunk}
            for future in as_completed(future_to_id):
                resultado = future.result()
                retornos.append(resultado)

    gerar_csv(retornos)
    return retornos


def gerar_csv(retornos: List[Dict], nome_arquivo: str = None):
    nome_arquivo = nome_arquivo or f"retornos_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    campos_csv = set()
    for retorno in retornos:
        campos_csv.update(retorno.keys())
        valor_retorno = retorno.get("retorno")

        if isinstance(valor_retorno, dict):
            if "error" in valor_retorno:
                campos_csv.add("retorno.error")
            elif "data" in valor_retorno and isinstance(valor_retorno["data"], list):
                if valor_retorno["data"]:
                    campos_csv.update(f"retorno.data.{k}" for k in valor_retorno["data"][0].keys())

    campos_csv.discard("retorno")
    campos_csv = sorted(campos_csv)

    with open(nome_arquivo, mode='w', newline='', encoding='utf-8') as arquivo_csv:
        writer = csv.DictWriter(arquivo_csv, fieldnames=campos_csv)
        writer.writeheader()

        for retorno in retornos:
            retorno_flat = {k: v for k, v in retorno.items() if k != "retorno"}
            valor_retorno = retorno.get("retorno")

            if isinstance(valor_retorno, dict):
                if "error" in valor_retorno:
                    retorno_flat["retorno.error"] = valor_retorno["error"]
                elif "data" in valor_retorno and isinstance(valor_retorno["data"], list):
                    if valor_retorno["data"]:
                        for k, v in valor_retorno["data"][0].items():
                            retorno_flat[f"retorno.data.{k}"] = v

            writer.writerow(retorno_flat)


if __name__ == "__main__":
    """Passa os Ids dos lojistas que deseja enviar para a CERC. OBS: Manda como string"""

    lista_requisitada = ["22877"]
    send_or_put_massive_v2(lista_para_envio=lista_requisitada)


# lista_requisitada = ["14685", "13528", "14166", "11851", "11641", "13052", "14715", "16730", "14539", "15798", "19165", "23794", "875", "13150", "1779", "1274", "2621", "23792", "2542", "2591", "2652", "2685", 
# "2736", "2549", "15629", "2229", "14052", "23826", "2525", "14133", "14550", "13909", "23837", "13157", "11977", "13743", "13070", "2272", "14728", "12778", "13999", "13568", "15026", "12775", 
# "14471", "13594", "14610", "15695", "12781", "11437", "3004", "2622", "2444", "2507", "12196", "526", "15056", "13235", "14581", "11448", "11811", "2999", "13035", "14497", "3005", "2441", 
# "12915", "15404", "4054", "11887", "14011", "15185", "14178", "15706", "14188", "13174", "13959", "14547", "12189", "2995", "2533", "2305", "2663", "13254", "12227", "14603", "1549", "643", 
# "13434", "1437", "13687", "13634", "2290", "12421", "13684", "12521", "11882", "13787", "11551", "13828", "12093", "15526", "1145", "14587", "15211", "13648", "14099", "2377", "13184", "2544", 
# "2452", "13218", "13815", "14554", "13550", "15018", "13056", "14968", "12576", "16721", "11686", "13894", "2572", "14775", "11475", "14203", "11465", "13054", "12714", "15682", "13759", "14772", 
# "14857", "13764", "12170", "13053", "15670", "12976", "15512", "14032", "14364", "14303", "14856", "15745", "12231", "11760", "14356", "14836", "11388", "11942", "11929", "12842", "15825", 
# "13794", "11421", "13176", "15569", "12194", "13549", "16661", "14734", "12380", "13011", "12945", "14739", "13966", "15171", "13978", "11802", "13928", "14664", "14491", "15810", "13715", 
# "11422", "13567", "11514", "12498", "12821", "13506", "11660", "12770", "12903", "11874", "15280", "13873", "14278", "11415", "13622", "12248", "13360", "14124", "12939", "11804", "14860", 
# "14301", "13662", "13357", "11474", "15480", "14153", "15371", "12313", "15151", "16720", "12543", "15344", "14592", "14841", "11798", "17485", "22877", "19840", "12697", "14216", "18326", 
# "19225", "17335", "20814", "20800", "19114", "14432", "19675", "11555", "11164", "11898", "21098", "19439", "23123", "11890", "13583", "18118", "18096", "15004", "21888", "19418", "13680", 
# "13043", "14140", "16502", "22005", "14797", "15314", "18430", "14293", "17487", "21666", "12420", "14037", "20665", "20163", "13878", "18782", "14551", "23207", "22878", "19453", "16312", 
# "20759", "19338", "12668", "19174", "19340", "13886", "19465", "11780", "13875", "18515", "13575", "19638", "16097", "21847", "14110", "12791", "12040", "15600", "17094", "19622", "21763", 
# "18760", "18461", "11679", "16581", "11517", "22038", "19355", "15697", "21633", "20500", "14105", "15489", "20837", "13343", "23200", "15960", "22918", "15840", "20494", "12394", "21529", 
# "14862", "22282", "23197", "13161", "14661", "23097", "18438", "23111", "21178", "14252", "19718", "20753", "18976", "23164", "18595", "17738", "15274", "20243", "23095", "14172", "18648", 
# "21026", "11754", "11772", "16702", "12761", "13079", "12875", "15419", "19934", "18065", "13971", "14427", "21740", "13943", "12021", "14378", "16571", "12067", "13838", "15153", "14788", 
# "19232", "15085", "16277", "12295", "13853", "21689", "13095", "20040", "22916", "12720", "14874", "21767", "13211", "16544", "18579", "12192", "18358", "12662", "21932", "21659", "13490", 
# "12724", "17483", "17105", "19257", "12076", "11805", "19170", "14762", "19105", "13889", "14144", "11935", "16453", "19094", "12925", "12480", "15418", "13530", "11985", "18190", "15054", 
# "19108", "16598", "18457", "19974", "19226", "16183", "13910", "19644", "14770", "19555", "18406", "15528", "20368", "15588", "16182", "22937", "12427", "19908", "12221", "19461", "20300", 
# "12611", "16374", "12024", "19682", "18532", "14125", "20318", "18672", "14702", "13319", "12056", "12580", "19660", "14713", "11381", "20657", "12290", "16151", "20280", "22970", "17129", 
# "17877", "13481", "14722", "12016", "15645", "13008", "18142", "23822", "17150", "12125", "18653", "13911", "11492", "20322", "17212", "23823", "14608", "23827", "21987", "23828", "23829", 
# "21474", "13588", "19372", "19429", "21247", "20234", "19781", "21669", "20812", "19601", "22890", "16972", "19917", "19269", "21626", "16367", "19981", "20184", "23209", "20359", "19758", 
# "18813", "16925", "16126", "23834", "14090", "16756", "12748", "16694", "23836", "16637", "17028", "15650", "17120", "18499", "13861", "16798", "17145", "18521", "17347", "17891", "16857", 
# "15099", "13709", "13392", "12496", "16061", "14510", "15276", "20632", "13344", "14058", "18597", "16883", "16572", "15139", "12349", "14694", "22994", "21560", "16191", "16945", "16145", 
# "22987", "16117", "14086", "16220", "19941", "15134", "19195", "16546", "19749", "15578", "18433", "11607", "17078", "23833", "15303", "19160", "15771", "20364", "19210", "23824", "20380", 
# "16024", "15701", "20353", "15223", "15658", "22927", "20827", "21273", "20327", "20680", "21693", "19763", "21382", "19357", "18915", "14436", "19795", "18372", "18053", "15466", "19413", 
# "19851", "13800", "18135", "23440", "22897", "19428", "19725", "17183", "16443", "5023", "20007", "21556", "23830", "18937", "19635", "22978", "23832", "18468", "19593", "15577", "23825", 
# "16039", "13234", "15978", "15590", "23838", "23831", "23835", "20793", "15762", "12157", "16330", "16979", "12523", "13326", "22880", "22942", "23132", "12256", "15087", "21482", "15090", 
# "16344", "22944", "14258", "22929", "16844", "12865", "15028", "15617", "14534", "17310", "22950", "20791", "22042", "16802", "20666", "13058", "23618", "16995", "14147", "13993", "19705", 
# "16474", "10721", "13088", "20093", "23640", "14962", "19241", "19498", "19213", "13976", "13986", "1640", "15168", "12383", "13850", "14839", "15113", "13584", "16282", "22879", "22959", 
# "22881", "16253", "14253", "14967", "14154", "14296", "14026", "14449", "14219", "22884", "22958", "14282", "22957", "14434", "13761", "13907", "13940", "14476", "23626", "14463", "23367", 
# "12929", "12843", "14118", "14234", "22340", "13328", "13099", "13888", "12696", "13555", "12882", "13478", "13320", "13475", "23034", "17038", "13135", "12398", "16127", "12949", "12672", 
# "12645", "12736", "12673", "12547", "14851", "13090", "12857", "20471", "13069", "12655", "22844", "22847", "16477", "22848", "22104", "15390", "22849", "22850", "22142", "2554", "22852", 
# "11549", "23389", "22853", "16628", "15035", "15429", "19198", "5503", "21027", "22854", "15411", "22319", "23201", "22855", "22856", "23390", "20844", "14393", "22858", "23161", "22859", 
# "11835", "21884", "14042", "23110", "22860", "12116", "21331", "14046", "11943", "13192", "18822", "13120", "22431", "13558", "13217", "22314", "18964", "14956", "18950", "19112", "18886", 
# "17019", "12504", "18361", "19943", "14323", "22329", "16416", "12430", "6514", "12469", "15828", "23060", "19591", "15265", "11528", "22896", "14698", "13871", "17349", "14699", "13345", 
# "23214", "15783", "19945", "23096", "23240", "13477", "12173", "23261", "20518", "11837", "11827", "11767", "19531", "23039", "12737", "21784", "15471", "22303", "13949", "22899", "15337", 
# "22762", "20373", "17371", "15262", "14801", "22034", "20036", "15131", "23037", "21445", "21832", "20802", "19379", "17462", "15400", "8029", "22032", "15014", "22872", "12762", "12003", 
# "22874", "16058", "22876", "22857", "22882", "22883", "22885", "22846", "22886", "22866", "22887", "19154", "12301", "22861", "22862", "16552", "14812", "22863", "14557", "19421", "16686", 
# "22864", "22865", "14595", "12814", "12340", "18091", "20682", "17298", "14262", "13520", "18034", "12390", "17015", "18342", "18561", "22867", "22868", "16288", "22869", "22870", "8840", 
# "20139", "22871", "13595", "16128", "22873", "22875", "15116", "14091", "16969", "17435", "12099", "18642", "13543", "11865", "11829", "15679", "11847", "20419", "18721", "11911", "14654", 
# "20737", "11796", "11839", "20022", "19903", "14614", "13460", "19557", "12685", "17300", "17320", "13428", "17223", "17132", "11715", "11962", "11667", "19507", "11795", "15503", "15261", 
# "15383", "12320", "16960", "21235", "11899", "14126", "21795", "13892", "16212", "21425", "12136", "18646", "12423", "20848", "20974", "15948", "13805", "15755", "19767", "13125", "11834", 
# "13182", "14158", "13094", "17493", "19829", "20762", "12448", "14354", "13504", "21406", "21585", "19575", "16799", "22321", "19879", "18306", "13038", "13308", "18566", "13713", "23400", 
# "14902", "11663", "19201", "13581", "19536", "16285", "12613", "18379", "11980", "16083", "11638", "20918", "12501", "13576", "14440", "20871", "12481", "243", "13529", "20534", "18495", 
# "18451", "20258", "13256", "20049", "20064", "21420", "19939", "18172", "18571", "22162", "19773", "11727", "13456", "21868", "16286", "12331", "15420", "21661", "18651", "20193", "22973", 
# "15807", "19468", "11476", "19481", "11396", "19366", "13984", "19406", "19220", "17199", "17188", "22069", "17130", "17134", "11354", "21902", "16217", "13413", "13014", "14138", "21982", 
# "16096", "12098", "17406", "15696", "16904", "16813", "21968", "16280", "13386", "13365", "19009", "12086", "22139", "13346", "18693", "18565", "16131", "12018", "17323", "13276", "18844", 
# "16604", "15572", "19802", "11841", "13501", "11815", "14160", "20067", "23134", "23155", "17215", "14970", "14495", "11761", "12346", "14742", "15612", "15581", "11708", "19038", "13814", 
# "12373", "14381", "14873", "21848", "21799", "16781", "19761", "15457", "18930", "21377", "15943", "16772", "23198", "14327", "14744", "20394", "14445", "17006", "16623", "13661", "21936", 
# "15895", "12923", "13937", "12415", "20761", "14383", "11707", "14326", "21995", "12690", "21522", "12784", "12031", "13742", "18687", "11758", "17299", "18756", "12722", "14620", "19746", 
# "11776", "16373", "13770", "18517", "18427", "17445", "12353", "22050", "16522", "14305", "19130", "12656", "16753", "20112", "20647", "16071", "16248", "14418", "18032", "17189", "14424", 
# "12627", "19331", "11590", "19422", "15368", "12413", "19056", "15889", "18202", "15815", "18257", "13758", "17100", "14012", "13414", "12006", "21396", "22074", "12831", "18818", "12885", 
# "15057", "13773", "12281", "17428", "23218", "15625", "13705", "18289", "11886", "23093", "12206", "12197", "23236", "12187", "12178", "11139", "23233", "12129", "15731", "17258", "11843", 
# "14271", "15127", "11997", "11700", "23125", "18580", "13158", "15394", "14359", "21346", "21757", "11908", "23100", "17101", "13220", "15788", "18138", "14465", "14667", "14396", "11752", 
# "15564", "15553", "18582", "19052", "21397", "15470", "11479", "17327", "12621", "19286", "15941", "14006", "22132", "19002", "21161", "21865", "19026", "21754", "13972", "19766", "19902", 
# "12108", "15252", "12772", "13697", "13930", "18046", "11777", "19030", "20715", "17176", "19256", "13879", "14330", "13860", "15479", "13893", "18555", "12955", "18193", "19510", "19443", 
# "20461", "12044", "12520", "14173", "14766", "22026", "20521", "14477", "12455", "14697", "21778", "21752", "12610", "21699", "13637", "15329", "12097", "21665", "21479", "14024", "12008", 
# "18301", "20339", "12049", "18944", "19982", "15804", "18180", "14016", "16251", "16356", "19670", "21080", "16513", "16233", "14352", "21807", "18266", "19409", "14794", "21664", "16648", 
# "14010", "16306", "16304", "12061", "11145", "12954", "12203", "11645", "18111", "18415", "11614", "17172", "14122", "17262", "19081", "19087", "19942", "16527", "18313", "12345", "12337", 
# "23053", "13514", "17348", "12924", "19417", "11755", "16543", "19244", "15180", "14846", "18530", "11709", "11666", "11850", "14226", "18441", "12280", "15086", "15484", "12276", "20646", 
# "15193", "14207", "15248", "18400", "16607", "19958", "14578", "14569", "15385", "15692", "20971", "18811", "13470", "15292", "16410", "18124", "13898", "12603", "12322", "18300", "19245", 
# "12702", "20427", "18834", "15770", "18223", "12488", "11613", "12463", "19368", "13127", "18663", "14200", "17495", "13480", "13591", "16207", "17412", "18889", "15863", "13269", "11896", 
# "11457", "20296", "12171", "15678", "18522", "15891", "13586", "12811", "16750", "12919", "12946", "18452", "20563", "15901", "11373", "11969", "15259", "12587", "18711", "18717", "11794", 
# "18763", "19451", "16311", "19115", "12866", "15055", "15722", "20756", "15358", "19895", "12182", "16996", "11742", "12050", "16856", "14350", "18593", "12132", "12432", "19664", "11972", 
# "14573", "19676", "16067", "19608", "12871", "14420", "19119", "20463", "16345", "18893", "14331", "13866", "18679", "20138", "13578", "15689", "18594", "12034", "13489", "12199", "17234", 
# "14156", "14329", "11718", "12910", "18896", "21789", "17084", "12999", "16307", "19281", "17373", "13577", "18259", "19901", "11674", "11964", "17036", "12399", "13196", "12401", "11533", 
# "19523", "20109", "13499", "17268", "16542", "17424", "14682", "14627", "13368", "16670", "11948", "21667", "17080", "11548", "13305", "16109", "15501", "14519", "12789", "16160", "13754", 
# "17407", "21993", "17221", "18875", "17271", "18448", "12824", "4009", "16213", "18527", "13611", "18201", "11583", "15183", "15320", "16463", "16092", "14907", "12614", "16651", "16456", 
# "17272", "16383", "17368", "19050", "11386", "15435", "15263", "17465", "14612", "12777", "16655", "13513", "16419", "16235", "12233", "13147", "18376", "19344", "19482", "15362", "18274", 
# "16032", "18801", "19036"]