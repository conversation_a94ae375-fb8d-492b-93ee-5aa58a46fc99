import json
import os
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Tuple

import requests

from api.helpers.custom_exceptions.cerc import CercException
from api.helpers.log_service import Logger
from api.models import DashboardLojista, DashboardDomiciliobancario

logger = Logger()
CERC_ENGINE_URL = os.environ.get("CERC_ENGINE_URL")
ENDPOINT = f'{CERC_ENGINE_URL}/credenciamento/'
ENDPOINT_OPTIN = f'{CERC_ENGINE_URL}/opt-in/'


def envio_cerc_engine(lojista_id: str, metodo: str):
    '''
    Metodo que envia o id do lojista para o cerc-engine.
    O ENDPOINT de envio é definido obtendo a variavel de ambiente CERC_ENGINE_URL.
    Os metodos apenas permitidos sao de POST e PUT.
    Quando se utiliza POST, se credencia o lojista na CERC. Se cadastrar um
    lojista ja cadastrado, ira levantar uma exceção informando o erro de operacao.
    Enquanto ao utilizar o PUT, se atualiza o lojista na CERC.

    Parameters:
        lojista_id (str): ID do lojista
        metodo (str): metodo de envio do requests.

    Returns:
        None
    '''

    metodo = metodo.upper()

    if not metodo in ["POST", "PUT"]:
        raise Exception('Essa função apenas aceita metodos POST ou PUT.')

    payload = json.dumps({
        "lojistas_id": [
            lojista_id
        ]
    })

    headers = {'Content-Type': 'application/json'}

    print(payload)

    try:
        response = requests.request(method=metodo, url=ENDPOINT, headers=headers, data=payload)
        response_json = response.json()

    except requests.exceptions.InvalidURL:
        print(f'URL invalida: {ENDPOINT}')
    except requests.exceptions.InvalidHeader:
        print(f'Header invalido: {headers}')
    else:
        # SIMULAÇÃO DE ERRO
        # raise CercException('9999', 'SIMULANDO ERRO CERC')
        if response.status_code in [201, 200]:
            for data in response_json['data']:
                if not data['status'] == '0':
                    raise CercException(data['erros'][0]['codigo'], data['erros'][0]['mensagem'])
        elif response.status_code in [400]:
            raise Exception(
                'error: {}'.format(response_json['error'])
            )


def envio_cerc_optin(lojista_id: int, operacao: str = "C"):
    '''
    Método para fazer envio de optin da cerc possibilitando o compartilhamento das agendas de recebíveis

    Parameters:
        lojista_id (int): ID do lojista
        operacao (str): Tipo de operação (C - criar / A - Alterar)

    Returns:
        None
    '''
    try:
        data_inicial: datetime = datetime.now() + timedelta(days=1)
        data_final: datetime = data_inicial + timedelta(days=1825)
        inicio_contrato: str = data_inicial.strftime("%Y-%m-%d")
        final_contrato: str = data_final.strftime("%Y-%m-%d")

        lojista = DashboardLojista.objects.get(pk=lojista_id)
        domicilio_bancario = DashboardDomiciliobancario.objects.get(lojista=lojista, ativo=True)

        payload_optin: list = [
            {
                "tipoOperacao": operacao,
                "referenciaExterna": str(uuid.uuid4()),
                "cnpjSolicitante": lojista.documento,
                "cnpjFinanciador": "23399607000191",  # CNPJ DA CERC
                "dataAssinaturaOptIn": inicio_contrato,
                "definicaoUnidadeRecebivel": {
                    "listaCnpjCredenciadora": [
                        "26794946000160"
                    ],
                    "listaCodigoArranjoPagamento": [
                        "99T"
                    ],
                    "dataInicio": inicio_contrato,
                    "dataFim": final_contrato,
                },
                "cnpjRecebedorAgenda": domicilio_bancario.documento_titular,
                "carteira": "",
            }
        ]
        response = requests.request(
            'POST' if operacao == 'C' else 'PUT',
            url=ENDPOINT_OPTIN,
            headers={'Content-Type': 'application/json'},
            data=json.dumps(payload_optin)
        )
        if response.status_code not in [200, 201]:
            raise Exception('Problema ao enviar o optin para a CERC, tente novamente')
        else:
            print(payload_optin)
            print(response.json())

    except (KeyError, DashboardDomiciliobancario.DoesNotExist, DashboardLojista.DoesNotExist, Exception) as e:
        raise Exception(str(e))


def envio_cerc_engine_sem_metodo_definido(lojista_id: str) -> Tuple[bool, dict]:
    """
    Demanda: SDESK-6524 - Envio massivo de lojistas para a CERC
    :param lojista_id: ID do lojista (em string)
    :return: (bool, dict)
    """
    try:
        instancia_lojista = DashboardLojista.objects.get(pk=int(lojista_id))
        payload = json.dumps({"lojistas_id": [str(lojista_id)]})
        headers = {'Content-Type': 'application/json'}
        if instancia_lojista.cerc_id is None:
            response = requests.post(url=ENDPOINT, headers=headers, data=payload)
            response_json = response.json()
            response.raise_for_status()
            logger.registrar(f"Resposta da CERC para envio via POST: {response_json}", "INFO")
            if response.status_code in [201, 200]:
                if 'data' in response_json:
                    data = response_json['data'][0]
                    instancia_lojista.cerc_id = data['referenciaExterna']
                    instancia_lojista.data_update = datetime.now()
                    instancia_lojista.save()
                    if data['status'] != 0:
                        logger.registrar("A tentativa de envio como um novo registro a CERC falhou", "ERROR")
                        return False, None
                    return True, response_json
        response = requests.put(url=ENDPOINT, headers=headers, data=payload)
        response_json = response.json()
        response.raise_for_status()
        logger.registrar(f"Resposta CER para o envio do tipo atualização: {response_json}", "INFO")
        if response.status_code in [201, 200]:
            if 'data' in response_json:
                data  = response_json['data'][0]
                instancia_lojista.cerc_id = data['referenciaExterna']
                instancia_lojista.data_update = datetime.now()
                instancia_lojista.save()
        return True, response_json
    except DashboardLojista.DoesNotExist as e:
        logger.registrar(f"O lojista com o ID {lojista_id} não foi encontrado, Erro: {e}", "CRITICAL")
        return False, {"error": str(e)}

    except CercException as e:
        logger.registrar(f"Erro ao tentar ENVIAR lojista {lojista_id} junto a CERC, erro: {e}", "ERROR")
        return False, {"error": str(e)}

    except KeyError as e:
        logger.registrar(f"Erro ao tentar ENVIAR lojista {lojista_id} junto a CERC, erro: {e}", "ERROR")
        return False, {"error": str(e)}
    except requests.exceptions.RequestException as e:
        logger.registrar(f"Erro ao tentar ENVIAR lojista {lojista_id} junto a CERC, erro: {e}", "ERROR")
        return False, {"error": str(e)}
    except IndexError as e:
        logger.registrar(f"Erro ao tentar ENVIAR lojista {lojista_id} junto a CERC, erro: {e}", "ERROR")
        return False, {"error": str(e)}
    except Exception as e:
        logger.registrar(f"Erro ao tentar ENVIAR lojista {lojista_id} junto a CERC, erro: {e}", "ERROR")
        return False, {"error": str(e)}



