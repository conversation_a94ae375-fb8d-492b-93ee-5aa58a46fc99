import logging
from requests import Session, RequestException
from django.conf import settings

logger = logging.getLogger(__name__)


def requester(func):
    def wrapper(*args, **kwargs):
        endpoint, session, data = func(*args, **kwargs)
        try:
            if data:
                r = session.post(endpoint, json=data, timeout=5)
            else:
                r = session.get(endpoint, timeout=5)
            
            try:
                data = r.json()
            except:
                return {}
            
            if not request_is_ok(r):
                logger.error(
                    'Falha na consulta na IDWall: {}, {}'
                    .format(func.__name__, r.status_code))
            return data
        except RequestException as e:
            logger.error(
                'Falha na consulta na IDWall: {}, {}'
                .format(func.__name__, e))
            return {}
    return wrapper


def request_is_ok(response):
    if response.status_code != 200:
        return False
    return True


class IdWall:

    def __init__(self):
        self.session = Session()
        self.endpoint = settings.IDWALL_ENDPOINT
        self.session.headers = {
            'Authorization': settings.IDWALL_TOKEN,
            'Content-Type': "application/json"}

    @requester
    def consulta_validacoes(self, numero):
        endpoint = self.endpoint + "/" + numero + "/validacoes"
        return (endpoint, self.session, None)

    @requester
    def consulta_resultado(self, numero):
        endpoint = '{}/{}/dados'.format(self.endpoint, numero)
        return (endpoint, self.session, None)

    @requester
    def relatorio_consulta(self, numero):
        endpoint = self.endpoint + "/" + numero + "/consultas"
        return (endpoint, self.session, None)

    @requester
    def create_relatorio(self, tipo_consulta, documento, data_nascimento= None):
        if tipo_consulta == 'lucree_QSA_pf':
            parametos = {
                'cpf_numero': documento,
                'cpf_data_de_nascimento': data_nascimento}
                
        elif tipo_consulta == 'lucree_QSA_pj':
            parametos = {
                'cnpj_numero': documento}

        data = {
            "matriz": tipo_consulta,
            "parametros": parametos}

        return (self.endpoint, self.session, data)
