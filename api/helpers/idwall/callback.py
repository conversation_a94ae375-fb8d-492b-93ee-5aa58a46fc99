from api.helpers.octadesk.send_webhook import post_octadesk_webhook
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido
from api.models import DashboardLojista
from api.helpers.post_processing.idwall_processing import IdwallProcessing
from api.helpers.cerc.cerc_engine import envio_cerc_engine


def idwall_callback(webhook):
    try:
        print(f"Rebendo payload pelo idwall_callback: {webhook}")
        protocolo = webhook['dados']['protocolo']
    except KeyError:
        return

    try:
        lojista = DashboardLojista.objects.get(numero_idwall=protocolo)
    except DashboardLojista.DoesNotExist:
        raise ValueError(
            'Lojista com o protocolo: "{}" não foi encontrado'
            .format(protocolo))

    IdwallProcessing(protocolo, lojista)

    if lojista.matriz_idwall == 'lucree_QSA_pf':
        # Atualiza a caralhada de pf
        IdwallProcessing.processing_pf(IdwallProcessing)
    else:
        # Atualiza a caralhada de pj
        IdwallProcessing.processing_pj(IdwallProcessing)

    if lojista.status_idwall == 'CONCLUIDO':
        envio_cerc_engine(lojista.id, "POST")
        post_rdstation_webhook(lojista.id)
        post_octadesk_webhook(lojista.id)

