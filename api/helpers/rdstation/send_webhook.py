from rest_framework import status
from api.models.produto import GpProduto
from api.models.rdstation import Sessions
from rest_framework.response import Response
from api.helpers.rdstation.utils.logger import rdstation_logger
from api.helpers.rdstation.utils.formatter import payload_formatter
from api.helpers.rdstation.utils.requester import envia_rdstation_webhook, find_user_by_email
from api.models import DashboardLojista, DashboardDepartamentos, AuthUser, Hierarquia, DashboardEnderecolojista, DashboardResponsavel, DashboardDomiciliobancario

def post_rdstation_webhook(id: int) -> Response:
    """
    Regra: Ao realizar cadastro de cliente ou atualizar, envia-o para o sistema RdStation.
    :return: Response
    """

    try:
        lojista: DashboardLojista = DashboardLojista.objects.get(pk=id)
        departamento: DashboardDepartamentos = None
        method = "POST" if lojista.rdstation_id is None else "PUT"
        if lojista.produto == 'Lucree Teste' or lojista.produto == 'Teste':
            raise ValueError(f'O produto do lojista é: {lojista.produto} e não pode ser enviado para RD Station.')
        if lojista.departamento:
            departamento = DashboardDepartamentos.objects.get(id=lojista.departamento)
        user = AuthUser.objects.filter(email=lojista.email_vendedor_responsavel).first()
        hierarquia = Hierarquia.objects.filter(id_usu=user.id).first() if user is not None else None
        endereco = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo='1').first()
        responsavel = DashboardResponsavel.objects.filter(lojista=lojista).first()
        grupo_negocio = GpProduto.objects.get(produto=lojista.produto)
        if responsavel is None:
            raise DashboardResponsavel().DoesNotExist
        if responsavel.email is None:
            raise DashboardResponsavel().DoesNotExist
        dados_bancarios = DashboardDomiciliobancario.objects.filter(lojista=lojista, ativo=True).first()
        rdstation_auth = Sessions.objects.first()
        if rdstation_auth is None:
                raise Sessions().DoesNotExist
    except DashboardLojista.DoesNotExist as e:
        rdstation_logger(lojista_id=id, rdstation_id=None, method="POST", payload={}, status_code=404, message=f"Falha ao localizar o lojista de id {id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Lojista não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except AuthUser.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ao localizar o usuário do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Usuário não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except DashboardEnderecolojista.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ao localizar o endereço do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Endereço não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except DashboardDomiciliobancario.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ao localizar o domicilio bancário do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Domicilio bancario não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except DashboardDepartamentos.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ao localizar o departamento do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Departamento não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except DashboardResponsavel.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ao localizar o responsável do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Responsável não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except GpProduto.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Falha ai localizar o grupo de negócio do lojista de id {lojista.id}. Verificar informações e enviar requisição novamente.")
        return Response({ "message": "Responsável não encontrado"}, status=status.HTTP_404_NOT_FOUND)
    except Sessions.DoesNotExist as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=404, message=f"Não existe nenhuma sessão de integração RD Station iniciada. Verificar documentação para gerar uma nova sessão.")
        return Response({ "message": "Não existe nenhuma sessão inciada no RD Station."}, status=status.HTTP_404_NOT_FOUND)
    except ValueError as e:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload={}, status_code=500, message=f"O produto do lojista é: {lojista.produto} e não pode ser enviado para RD Station.")
        return Response({ "message": e.args[0]}, status=status.HTTP_404_NOT_FOUND)

    rdstation_user = find_user_by_email(responsavel.email, rdstation_auth)
    if rdstation_user.status_code == 200 and lojista.rdstation_id is None:
        lojista.rdstation_id = rdstation_user.json()['uuid']
        lojista.save()

    payload_final = payload_formatter(lojista, hierarquia, endereco, responsavel, dados_bancarios, departamento, grupo_negocio)
    #payload_final.pop('email')
    response, error = envia_rdstation_webhook(payload_final=payload_final, rdstation_auth=rdstation_auth, rdstation_id=lojista.rdstation_id)
    if error:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload=payload_final, status_code=500, message=f"Problema no envio de informações para o RD Station. Detalhes: {error}")
        print(f'Problema no envio para o RD Station. Detalhes: {error}')
        return Response({ "message": f"Falha nas variáveis de ambiente. Detalhes: {error}" }, status=500)

    if response.status_code != 200:
        rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload=payload_final, status_code=response.status_code, message=response.json()['errors'])
        return Response({ "message": f"Falha ao enviar webhook. Detalhes: {response.json()['errors']}"}, status=response.status_code)

    if lojista.rdstation_id is None:
        lojista.rdstation_id = response.json().get('uuid')
        lojista.save()

    rdstation_logger(lojista_id=lojista.id, rdstation_id=lojista.rdstation_id, method=method, payload=payload_final, status_code=response.status_code, message=f"Usuário enviado para o RD Station com sucesso.")
    print(f"[WH] Cadastro enviado com sucesso para o RD Station.")
    return Response({ "message": "Webhook enviado com sucesso" }, status=response.status_code)
