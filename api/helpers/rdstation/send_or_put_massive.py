import os
from pprint import pprint

import django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "credenciamento.settings")
django.setup()
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.models import DashboardLojista


def put_massive():
    """

    Use este recurso para eventuais atualizações em massa de lojistas no RD Station.
    Preencha a lista com os Ids dos lojistas e execute o script.
    """
    list_errors_ids = [23059]
    list_success_docs = []

    qs = DashboardLojista.objects.filter(data_update__gte='2024-11-20')
    iterator = 0
    for lojista in qs:
        try:
            post_rdstation_webhook(lojista.id)
            iterator += 1
            list_success_docs.append(f"{lojista.documento} | {lojista.nome}")
            print(
                f"Lojista de id {lojista.id} atualizado no RD Station. {iterator}/{qs.count()}")
        except Exception as e:
            print(f"Erro ao enviar lojista de id {lojista.id}: {e}")
    pprint(list_success_docs)

if __name__ == "__main__":
    put_massive()
