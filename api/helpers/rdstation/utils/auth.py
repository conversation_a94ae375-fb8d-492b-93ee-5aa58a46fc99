from typing import Union
import requests

from api.models.rdstation import Sessions
from credenciamento import settings

def renew_access_token(session: Sessions) -> Union[dict, None]:
    url = settings.RDSTATION_BASE_PATH + "/auth/token"

    payload = {
        "client_id": settings.RDSTATION_CLIENT_ID,
        "client_secret": settings.RDSTATION_CLIENT_SECRET,
        "refresh_token": session.refresh_token
    }

    headers = {
        "accept": "application/json",
        "content-type": "application/json"
    }

    try:
        response = requests.post(url, json=payload, headers=headers)
        access_token = response.json().get('access_token')
        refresh_token = response.json().get('refresh_token')
        session.access_token = access_token
        session.refresh_token = refresh_token
        session.save()
        return { "access_token": access_token, "refresh_token": refresh_token }
    except Exception as error:
        print(f"Erro ao obter/salvar token. Detalhes: {error}")
        return None

def validate_access_token(access_token: str) -> bool:
    url = "https://api.rd.services/platform/contacts/email:<EMAIL>"

    headers = {
       "accept": "application/json",
       "authorization": "Bearer " + access_token
    }

    response = requests.get(url, headers=headers)

    if response.status_code != 200:
       return False

    return True