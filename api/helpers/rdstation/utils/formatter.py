import unidecode


def fase_format(fase: str) -> str:
    fase = 'cf_' + fase
    fase = fase.lower().replace(' ', '_')
    fase = fase.replace(')', '')
    fase = fase.replace('(', '')
    fase = unidecode.unidecode(fase)
    return fase


def payload_formatter(lojista, hierarquia, endereco, responsavel, dados_bancarios, departamento, grupo_negocio) -> dict:
    payload_final = {}

    payload_final["name"] = lojista.nome if lojista.nome is not None else 'Nome do lojista'
    payload_final["cf_lojista_documento"] = lojista.documento
    payload_final["cf_num_terminais"] = lojista.num_terminais
    payload_final["cf_terminais_transacionando"] = 0
    payload_final["cf_negocio"] = lojista.produto
    payload_final["cf_grupo_negocio"] = grupo_negocio.gpproduto

    fases = eval(lojista.fase)
    for fase in fases:
        fase_formatted = fase_format(fase['fase'])
        payload_final[fase_formatted] = fase['status']

    has_link_pagamento = False
    has_maquina_fisica = False
    if departamento:
        descricaoDepartamento = departamento.descricao_departamento if departamento.descricao_departamento is not None else 'N/A'
        payload_final["cf_mcc"] = descricaoDepartamento if descricaoDepartamento is not None else 'N/A'
    else:
        payload_final["cf_mcc"] = 'N/A'
    payload_final["cf_fase_integracao"] = lojista.fase_integracao
    data_cadastro = lojista.data_cadastro
    data_update = lojista.data_update

    if data_cadastro == None and data_update == None:
        payload_final["cf_data_cadastro"] = None
    else:
        payload_final["cf_data_cadastro"] = lojista.data_cadastro.strftime(
            "%Y-%m-%d") if lojista.data_cadastro is not None else lojista.data_update.strftime("%Y-%m-%d")

    if lojista.flag_link_pagto is not None and lojista.flag_link_pagto >= 3:
        has_link_pagamento = True

    if hierarquia is not None:
        payload_final[
            'cf_agente_comercial_nome'] = hierarquia.usu_nome if hierarquia is not None else 'Não possui hierarquia'
        payload_final['cf_agente_comercial_email'] = lojista.email_vendedor_responsavel
        payload_final[
            'cf_agente_comercial_cargo_hierarquia'] = hierarquia.cargo.descricao if hierarquia is not None else 'Não possui hierarquia'
        payload_final['cf_agente_comercial_projeto'] = lojista.produto

    if lojista.plano is not None:
        if hasattr(lojista, 'operadora'):
            payload_final['cf_operadora_nome'] = lojista.operadora.nome if lojista.operadora is not None else 'N/A'

    else:
        payload_final['cf_operadora_nome'] = 'N/A'

    has_maquina_fisica = True

    if lojista.plano_virtual is not None:
        has_link_pagamento = True

    if endereco is not None:
        payload_final["cf_endereco_cep"] = endereco.cep if endereco.cep is not None else 'N/A'
        payload_final["cf_endereco_bairro"] = endereco.bairro if endereco.bairro is not None else 'N/A'
        payload_final["city"] = endereco.cidade if endereco.cidade is not None else 'N/A'
        payload_final["state"] = endereco.estado if endereco.estado is not None else 'NA'
        payload_final["cf_endereco_numero"] = endereco.numero if endereco.numero is not None else 'N/A'
        payload_final["cf_endereco_logradouro"] = endereco.logradouro if endereco.logradouro is not None else 'N/A'
        payload_final["cf_endereco_complemento"] = endereco.complemento if endereco.complemento is not None else 'N/A'

    payload_final['email'] = responsavel.email
    payload_final['cf_responsavel_nome'] = responsavel.nome if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_email'] = responsavel.email if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_ocupacao'] = responsavel.ocupacao if responsavel is not None else 'N/A'
    payload_final[
        'cf_responsavel_telefone'] = "55" + responsavel.ddd + responsavel.telefone if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_documento'] = responsavel.documento if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_profissao'] = responsavel.profissao if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_nacionalidade'] = responsavel.nacionalidade if responsavel is not None else 'N/A'
    payload_final['cf_responsavel_data_nascimento'] = responsavel.data_nascimento if responsavel is not None else 'N/A'

    if dados_bancarios is not None:
        payload_final['cf_dados_bancarios_banco'] = dados_bancarios.banco if dados_bancarios is not None else None
        payload_final['cf_dados_bancarios_conta'] = str(dados_bancarios.conta) if dados_bancarios is not None else None
        payload_final['cf_dados_bancarios_agencia'] = dados_bancarios.agencia if dados_bancarios is not None else None
        payload_final[
            'cf_dados_bancarios_tipo_conta'] = dados_bancarios.tipo_conta if dados_bancarios is not None else None
        payload_final[
            'cf_dados_bancarios_digito_conta'] = dados_bancarios.digito_conta if dados_bancarios is not None else None
        payload_final[
            'cf_dados_bancarios_documento_titular'] = "CPF" if dados_bancarios.matriz_titular == "lucree_QSA_pf" else "CNPJ"
        payload_final[
            'cf_dados_bancarios_nome_titular'] = dados_bancarios.nome_titular if dados_bancarios.nome_titular is not None else 'Nome do titular'
        payload_final['cf_dados_bancarios_documento_titular'] = dados_bancarios.documento_titular

    payload_final['cf_link_pagamento'] = has_link_pagamento
    payload_final['cf_maquina_fisica'] = has_maquina_fisica
    payload_final['tags'] = ['cliente convertido sist lucree']

    payload_final['legal_bases'] = [{
        "category": "communications",
        "type": "legitimate_interest",
        "status": "granted"
    }]

    list_error_fields = [
        ('cf_anlise_id_wall', 'cf_analise_id_wall'),
        ('cf_anlise_de_documentao', 'cf_analise_de_documentacao'),
        ('cf_documentao_pendente', 'cf_documentacao_pendente'),
        ('cf_documentao_aprovada', 'cf_documentacao_aprovada'),
        ('cf_documentao_reprovada', 'cf_documentacao_reprovada'),
        ('cf_aprovado_gesto_de_risco', 'cf_aprovado_gestao_de_risco'),
        ('cf_anlise_gesto_de_risco', 'cf_analise_gestao_de_risco'),
        ('cf_aprovado_gesto_de_risco', 'cf_aprovado_gestao_de_risco'),
        ('cf_reprovado_gesto_de_risco', 'cf_reprovado_gestao_de_risco'),
        ('cf_problema_na_integrao_4ward', 'cf_problema_na_integracao_4ward'),
        ('cf_em_aprovao_comercial', 'cf_em_aprovacao_comercial'),
        ('cf_expedio', 'cf_expedicao'),
        ('cf_mquina_enviada', 'cf_maquina_enviada'),
        ('cf_mquina_entregue', 'cf_maquina_entregue'),
        ('cf_preparao_da_mquina', 'cf_preparacao_da_maquina'),
        ('cf_envio_de_mquina_negado', 'cf_envio_de_maquina_negado'),
        ('cf_pagamento_em_confirmao', 'cf_pagamento_em_confirmacao'),
        ('cf_pagamento_no_confirmado', 'cf_pagamento_nao_confirmado'),
    ]

    """
    HOTFIX SCA-1547 em 16 de outubro de 2024.
    Percorre todas as chaves do dicionário e verifica se existe alguma chave que está na lista de erro, caso exista,
    o valor da chave é removido e adicionado com o valor correto.
    Justificativa: O banco possui alguns registros com caracteres inválidos, que o método payload_formatter não consegue
    tratar, por isso, foi necessário criar um hotfix para corrigir esses registros.
    """
    for k, v in list_error_fields:
        if k in payload_final:
            valor = payload_final[k]
            del payload_final[k]
            payload_final[v] = valor

    return payload_final
