import json
from typing import Any, Union

import requests

from api.helpers.rdstation.utils.auth import renew_access_token, \
    validate_access_token
from api.models.rdstation import Sessions
from credenciamento import settings
from credenciamento.log_service import Logger

logger = Logger()


def envia_rdstation_webhook(payload_final: dict, rdstation_auth: Sessions, rdstation_id: Union[str, None] = None) -> \
Union[Union[requests.Response, None], bool]:
    error = False

    is_token_valid = validate_access_token(rdstation_auth.access_token)
    if not is_token_valid:
        renew_access_token(session=rdstation_auth)
        rdstation_auth = Sessions.objects.first()

    contacts_path = settings.RDSTATION_BASE_PATH + "/platform/contacts" if settings.RDSTATION_BASE_PATH is not None else None
    crm_path = " https://crm.rdstation.com/api/v1/contacts/"

    if contacts_path is None:
        error = True
        return None, error

    integration_headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + rdstation_auth.access_token
    }

    if rdstation_id is None:
        response_final = requests.post(contacts_path, data=json.dumps(payload_final), headers=integration_headers)
        return response_final, error

    update_person_url = f"{contacts_path}/email:{payload_final['email']}"
    payload_final.pop('email')
    response_final = requests.patch(update_person_url, data=json.dumps(payload_final), headers=integration_headers)
    if response_final.status_code != 200:
        logger.registrar(f"Erro ao atualizar contato no RD Station: {response_final.json()}")
    return response_final, error


def find_user_by_email(email: str, rdstation_auth: Sessions) -> Any:
    is_token_valid = validate_access_token(rdstation_auth.access_token)
    if not is_token_valid:
        renew_access_token(session=rdstation_auth)
        rdstation_auth = Sessions.objects.first()

    contacts_path = settings.RDSTATION_BASE_PATH + f'/platform/contacts/email:{email}' if settings.RDSTATION_BASE_PATH is not None else None

    if contacts_path is None:
        raise ValueError("Variável de ambiente base do RD Station está faltando.")

    integration_headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + rdstation_auth.access_token
    }

    response = requests.get(contacts_path, headers=integration_headers)

    return response
