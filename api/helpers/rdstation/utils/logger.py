from api.models.rdstation import Logs

def rdstation_logger(lojista_id, rdstation_id, method, payload, status_code, message):
    try:
        Logs.objects.create(
            lojista_id=lojista_id,
            rdstation_id=rdstation_id,
            method=method,
            payload=payload,
            status_code=status_code,
            message=message
        )
    except Exception as error:
        print(f'Falha ao registrar logs: Schema: rdstation -> tabela: logs. Detalhes: {error}')