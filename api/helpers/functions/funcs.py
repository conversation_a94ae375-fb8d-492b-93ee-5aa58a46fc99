from datetime import datetime
import json
from api.helpers.idwall.idwall import IdWall
from api.helpers.pendentes import send_pendente

# Converte um nome completo para first_name e last_name 
def parse_name(nome : str):
    if not nome:
        return nome

    if not isinstance(nome, str):
        nome = str(nome)

    names : list(str) = nome.split()
    return names[0][:30], " ".join(names[1:])[:150]

class Funcs:
    @staticmethod
    def atualiza_fase(fases_list, fases_concluidas, proxima_fase):
        for fase in fases_list:
            for fase_concluida in fases_concluidas:
                if fase['fase'] == fase_concluida:
                    fase['status'] = 'concluida'
                    fase['dt_alteracao'] = str(datetime.now())
                elif fase['fase'] == proxima_fase:
                    fase['status'] = 'em_andamento'

        return json.dumps(fases_list)

    @staticmethod
    def desabilita_fase(fases_list, fases_desabilitar):
        fases_list = json.loads(fases_list)
        for fase_desabilitar in fases_desabilitar:
            for fase in fases_list:
                if fase['fase'] == fase_desabilitar:
                    fase['status'] = 'nao_considerar'
        return json.dumps(fases_list)

    @staticmethod
    def fase_atual(fases_list):
        try:
            fases = json.loads(fases_list)
            for d in fases:
                if d['status'] == 'em_andamento':
                    return d['fase']
        except Exception as e:
            print(str(e))
    @staticmethod
    def send_idwall(client,data_nascimento):
        idwall = IdWall()
        if client.matriz_idwall == 'lucree_QSA_pf':
            idwall_relatorio = idwall.create_relatorio(
                client.matriz_idwall, client.documento,data_nascimento)
        else:
            idwall_relatorio = idwall.create_relatorio(
                client.matriz_idwall, client.documento)

        if idwall_relatorio.get("error"):
            message = idwall_relatorio.get("message")
            send_pendente(client, "IDWALL", "envio",message)
            return Exception(message)

        idwall_numero = idwall_relatorio.get('result', {}).get('numero', '')
        client.numero_idwall = idwall_numero
        return idwall_relatorio, True
