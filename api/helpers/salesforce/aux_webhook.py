import json

import requests

from api.models import WebhooksCredenciamento, DashboardLojista, DashboardDepartamentos, AuthUser, Hierarquia, \
    DashboardCessao, DashboardEnderecolojista, DashboardResponsavel, DashboardDomiciliobancario, DashboardOperadoraGprs
from rest_framework.response import Response
from rest_framework import status
from api.models.webhook_log import WebhookLog
from credenciamento import settings

def post_webook(id: int):
    """
    Regra: Ao realizar um cadastro ou alteração deve ser enviado um Hook
    para o sistema SalesForce com os dados relacionados a este cliente
    Esta função deve receber o id do cliente, buscar os dados necessários e enviar o hook
    :return: Response
    """
    payload_operadora = {}
    payload_agente = {}
    payload_plano = []
    payload_documentos = []
    payload_produtos = []
    payload_final = {}

    webhooks_cadastrados = WebhooksCredenciamento.objects.filter()
    try:
        lojista = DashboardLojista.objects.get(pk=id)
        if lojista.produto != 'Lucree Teste' or lojista.produto != 'Teste':
            payload_final["lojista_id"] = lojista.id
            payload_final["user_id"] = lojista.lojista_id if lojista.lojista_id is not None else '0'
            payload_final["nome"] = lojista.nome if lojista.nome is not None else 'Nome do lojista'
            payload_final["documento"] = lojista.documento
            payload_final["num_terminais"] = lojista.num_terminais
            payload_final["terminais_transacionando"] = 0

            descricaoDepartamento = 'N/A'
            if (lojista.departamento):
                departamento = DashboardDepartamentos.objects.get(id=lojista.departamento)
                descricaoDepartamento = departamento.descricao_departamento if departamento.descricao_departamento is not None else 'N/A'
            payload_final["mcc"] = descricaoDepartamento

            payload_final["fase_integracao"] = lojista.fase_integracao
            data_cadastro = lojista.data_cadastro
            data_update = lojista.data_update

            if data_cadastro == None and data_update == None:
             payload_final["data_cadastro"] = None
            else:
             payload_final["data_cadastro"] = lojista.data_cadastro.strftime("%Y-%m-%d") if lojista.data_cadastro is not None else lojista.data_update.strftime("%Y-%m-%d")

            if lojista.flag_link_pagto is not None and lojista.flag_link_pagto >= 3:
                payload_produtos.append('Link de pagamento')

            user = AuthUser.objects.filter(email=lojista.email_vendedor_responsavel).first()

            hierarquia = Hierarquia.objects.filter(id_usu=user.id).first() if user is not None else None

            payload_agente['nome'] = hierarquia.usu_nome if hierarquia is not None else 'Não possui hierarquia'
            payload_agente['email'] = lojista.email_vendedor_responsavel
            payload_agente['cargo_hierarquia'] = hierarquia.cargo.descricao if hierarquia is not None else 'Não possui hierarquia'
            payload_agente['projeto'] = lojista.produto
            payload_final["agente_comercial"] = payload_agente

            if lojista.plano is not None:
              if hasattr(lojista, 'operadora'):
                payload_operadora["id"] = lojista.operadora.id if lojista.operadora is not None else 'N/A'
                payload_operadora["nome"] = lojista.operadora.nome if lojista.operadora is not None else 'N/A'
                payload_operadora["identificador"] = lojista.operadora.identificador if lojista.operadora is not None else 'N/A'
                payload_operadora["provedor"] = lojista.operadora.provedor if lojista.operadora is not None else 'N/A'
              else:
                lojista.operadora =  None

              payload_plano.append({
                  'id': lojista.plano.id,
                  'nome': lojista.plano.nome if lojista.plano is not None else 'N/A',
                  'tipo_contratacao': lojista.plano.tipo_contratacao
              })
              payload_produtos.append('Máquina física')

            payload_final["operadora"] = payload_operadora

            if lojista.plano_virtual is not None:
                payload_plano.append({
                    'id': lojista.plano_virtual.id,
                    'nome': lojista.plano_virtual.nome if lojista.plano_virtual is not None else 'N/A',
                    'tipo_contratacao': 'VIRTUAL'
                })
                payload_produtos.append('Link de pagamento')
            documentos = DashboardCessao.objects.filter(lojista=lojista)
            for documento in documentos:
                nomeArquivo = documento.foto_documento
                vetor = nomeArquivo.split(".")
                formato = vetor[-1]
                # documento = build_docs(documento)
                payload_documentos.append({
                    "documento": documento.foto_documento,
                    "tipo_arquivo": documento.tipo_arquivo,
                    "formato": formato
                })
            payload_final['documentos'] = payload_documentos

            endereco = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo='1').first()
            payload_endereco = {
                "cep": endereco.cep if endereco.cep is not None  else 'N/A',
                "tipo": endereco.tipo if endereco.tipo is not None else 'N/A',
                "bairro": endereco.bairro if endereco.bairro is not None else 'N/A',
                "cidade": endereco.cidade if  endereco.cidade is not None else 'N/A',
                "estado": endereco.estado if endereco.estado is not None else 'NA',
                "numero": endereco.numero if endereco.numero is not None else 'N/A',
                "principal": endereco.principal if endereco.principal is not None else False,
                "logradouro": endereco.logradouro if endereco.logradouro is not None else 'N/A',
                "complemento": endereco.complemento if endereco.complemento is not None else 'N/A'
            }

            payload_final['endereco'] = payload_endereco

            responsavel = DashboardResponsavel.objects.filter(lojista=lojista).first()

            payload_responsavel = {
                "nome": responsavel.nome if responsavel is not None else 'N/A',
                "sexo": responsavel.sexo if responsavel is not None else 'N/A',
                "email": responsavel.email if responsavel is not None else 'N/A',
                "ocupacao": responsavel.ocupacao if responsavel is not None else 'N/A',
                "telefone": '{:0>3}'.format(responsavel.ddd) + responsavel.telefone if responsavel is not None else '',
                "documento": responsavel.documento if responsavel is not None else 'N/A',
                "profissao": responsavel.profissao if responsavel is not None else 'N/A',
                "nacionalidade": responsavel.nacionalidade if responsavel is not None else 'N/A',
                "data_nascimento": responsavel.data_nascimento if responsavel is not None else 'N/A'
            }

            payload_final['responsavel'] = payload_responsavel

            dados_bancarios = DashboardDomiciliobancario.objects.filter(lojista=lojista, ativo=True).first()

            payload_dadosbancarios = {
                "banco": dados_bancarios.banco if dados_bancarios is not None else None,
                "conta": str(dados_bancarios.conta) if dados_bancarios is not None else None,
                "agencia": dados_bancarios.agencia if dados_bancarios is not None else None,
                "tipo_conta": dados_bancarios.tipo_conta if dados_bancarios is not None else None,
                "digito_conta": dados_bancarios.digito_conta if dados_bancarios is not None else None,
                "tipo_documento_titular": "CPF" if dados_bancarios.matriz_titular == "lucree_QSA_pf" else "CNPJ",
                "nome_titular": dados_bancarios.nome_titular if dados_bancarios.nome_titular is not None else 'Nome do titular',
                "documento_titular": dados_bancarios.documento_titular
            }
            payload_final['planos'] = payload_plano
            payload_final['produtos'] = payload_produtos
            payload_final['dados_bancarios'] = payload_dadosbancarios

    except DashboardLojista.DoesNotExist as e:
        return Response("Lojista não encontrado", status=status.HTTP_404_NOT_FOUND)
    except DashboardOperadoraGprs.DoesNotExist as e:
        return Response("Operadora não encontrada", status=status.HTTP_404_NOT_FOUND)
    except AuthUser.DoesNotExist as e:
        return Response("Usuário não encontrado", status=status.HTTP_404_NOT_FOUND)
    except DashboardCessao.DoesNotExist as e:
        return Response("Documentos não encontrados", status=status.HTTP_404_NOT_FOUND)
    except DashboardEnderecolojista.DoesNotExist as e:
        return Response("Endereço não encontrado", status=status.HTTP_404_NOT_FOUND)
    except DashboardDomiciliobancario.DoesNotExist as e:
        return Response("Domicilio bancario não encontrado", status=status.HTTP_404_NOT_FOUND)
    except DashboardDepartamentos.DoesNotExist as e:
        return Response("Departamento não encontrado", status=status.HTTP_404_NOT_FOUND)

    envia_webhook(webhooks_cadastrados, payload_final)
    return Response("Webhook enviado com sucesso", status=status.HTTP_200_OK)


def envia_webhook(webhooks_cadastrados, payload_final):
    sucesso = False
    headers = {'Content-Type': "application/json"}

    for hooks in webhooks_cadastrados:
        lista_final = []
        if hooks.has_authentication:
            url = hooks.url_auth
            payload = {
                'username': hooks.username,
                'password': hooks.password,
                'grant_type': 'password',
                'client_id': settings.CLIENTE_ID,
                'client_secret': settings.CLIENTE_SECRET
            }
            response = requests.request("POST", url, headers={}, data=payload)
            auth = response.json()
            chave = auth['token_type'] + ' ' + auth['access_token']
            headers = {'Content-Type': "application/json", 'Authorization': chave}

        lista_final.append(payload_final)
        response_final = requests.post(hooks.endpoint, data=json.dumps(lista_final), headers=headers)

        WebhookLog.objects.create(
            id_webhook = hooks.id,
            request_payload = json.dumps(lista_final),
            response_body = response_final.text,
            response_status = response_final.status_code
        )

        print(f'[WH] response final status {response_final.status_code}')
        print(f'[WH] response final text {response_final.text}')
        sucesso = True if response_final.status_code in [200, 201] else False
    return sucesso
