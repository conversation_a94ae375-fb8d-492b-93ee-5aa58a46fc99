import os
from os.path import exists

import django
import logging
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "credenciamento.settings")
django.setup()
from api.helpers.octadesk.utils.merchants import update_merchant
from api.models import DashboardLojista


from api import models as models_api
from api.helpers.octadesk.utils.requester import find_octadesk_user, \
    send_octadesk_webhook

logging.basicConfig(level=logging.INFO)


class OctadeskMassive:
    def __init__(self):
        self.octadesk_auth = models_api.OctaSessions.objects.all().first()
    def get_endereco(self,
                      lojista: models_api.DashboardLojista) -> (
            bool, models_api.DashboardEnderecolojista):
        try:
            endereco = models_api.DashboardEnderecolojista.objects.filter(lojista=lojista).filter(tipo='1').first()
            return True, endereco
        except models_api.DashboardEnderecolojista.DoesNotExist:
            return False, None
    def get_operadora(self,
                      lojista: models_api.DashboardLojista) -> (
            bool, models_api.DashboardOperadoraGprs):
        try:
            operadora = lojista.operadora
            return True, operadora
        except models_api.DashboardOperadoraGprs.DoesNotExist:
            return False, None

    def get_responsavel(self,
                        lojista: models_api.DashboardLojista) -> (
            bool, models_api.DashboardResponsavel):
        try:
            responsavel = models_api.DashboardResponsavel.objects.filter(
                lojista=lojista).first()
            return True, responsavel
        except models_api.DashboardResponsavel.DoesNotExist:
            return False, None

    def get_dados_bancarios(self,
                            lojista: models_api.DashboardLojista) -> (
            bool, models_api.DashboardDomiciliobancario):
        try:
            domicilio = models_api.DashboardDomiciliobancario.objects.filter(
                lojista=lojista).first()
            return True, domicilio
        except models_api.DashboardDomiciliobancario.DoesNotExist:
            return False, None

    def build_payload(self, lojista: models_api.DashboardLojista) -> (
            bool, dict):
        payload = {}
        custom_fields = {}
        contact = {}
        phone_contacts = []
        has_maquina_fisica: bool = False
        has_link_pagamento: bool = False
        custom_fields['lojista_id'] = lojista.id
        custom_fields[
            'user_id'] = lojista.lojista_id if lojista.lojista_id is not None else '0'

        custom_fields[
            'razao_social'] = lojista.nome if lojista.nome is not None else 'Razao Social do Lojista'
        custom_fields['documento'] = lojista.documento
        custom_fields[
            'num_terminais']: int = lojista.num_terminais if lojista.nome is not None else 0
        custom_fields['terminais_transacionando'] = 0
        try:
            auth_user = models_api.AuthUser.objects.get(
                email=lojista.email_vendedor_responsavel)
            hieararquia = models_api.Hierarquia.objects.get(
                id_usu=auth_user.id)
            custom_fields['agente_comercial_nome'] = hieararquia.usu_nome
            custom_fields[
                'agente_comercial_email'] = lojista.email_vendedor_responsavel
            custom_fields[
                'agente_comercial_cargo_hierarquia'] = hieararquia.cargo.descricao
            custom_fields[
                'agente_comercial_projeto'] = lojista.produto if lojista.produto is not None else 'Projeto não informado'
            custom_fields[
                'agente_comercial_produto'] = lojista.produto if lojista.produto is not None else 'Produto não informado'
        except models_api.AuthUser.DoesNotExist:
            custom_fields['agente_comercial_nome'] = 'Não possui hierarquia'
            custom_fields[
                'agente_comercial_email'] = lojista.email_vendedor_responsavel if lojista.email_vendedor_responsavel is not None else 'Email do vendedor responsável indisponível'
            custom_fields[
                'agente_comercial_cargo_hierarquia'] = 'Não possui hierarquia'
            custom_fields[
                'agente_comercial_projeto'] = lojista.produto if lojista.produto is not None else 'Projeto não informado'
            custom_fields[
                'agente_comercial_produto'] = lojista.produto if lojista.produto is not None else 'Produto não informado'
        except models_api.AuthUser.DoesNotExist:
            custom_fields['agente_comercial_nome'] = 'Não possui hierarquia'
            custom_fields[
                'agente_comercial_email'] = lojista.email_vendedor_responsavel if lojista.email_vendedor_responsavel is not None else 'Email do vendedor responsável indisponível'
            custom_fields[
                'agente_comercial_cargo_hierarquia'] = 'Não possui hierarquia'
            custom_fields[
                'agente_comercial_projeto'] = lojista.produto if lojista.produto is not None else 'Projeto não informado'
            custom_fields[
                'agente_comercial_produto'] = lojista.produto if lojista.produto is not None else 'Produto não informado'
        custom_fields["fase_integracao"] = lojista.fase_integracao
        data_cadastro = lojista.data_cadastro
        data_update = lojista.data_update
        if data_cadastro == None and data_update == None:
            custom_fields["data_cadastro"] = None
        else:
            custom_fields["data_cadastro"] = lojista.data_cadastro.strftime(
                "%Y-%m-%d") if lojista.data_cadastro is not None else lojista.data_update.strftime("%Y-%m-%d")
        if lojista.plano is not None:
            if hasattr(lojista, 'operadora'):
                custom_fields[
                    'operadora_id'] = lojista.operadora.id if lojista.operadora is not None else 'N/A'
                custom_fields[
                    'operadora_nome'] = lojista.operadora.nome if lojista.operadora is not None else 'N/A'
                custom_fields[
                    'operadora_identificador'] = lojista.operadora.identificador if lojista.operadora is not None else 'N/A'
                custom_fields[
                    'operadora_provedor'] = lojista.operadora.provedor if lojista.operadora is not None else 'N/A'
            else:
                custom_fields['operadora_id'] = 'N/A'
                custom_fields['operadora_nome'] = 'N/A'
                custom_fields['operadora_identificador'] = 'N/A'
                custom_fields['operadora_provedor'] = 'N/A'
            has_maquina_fisica = True
        if lojista.plano_virtual is not None:
            has_link_pagamento = True
        """Dados do endereço"""
        exists, endereco = self.get_endereco(lojista=lojista)
        if exists:
            custom_fields[
                'endereco_cep'] = endereco.cep if endereco.cep is not None else 'N/A'
            custom_fields[
                'endereco_bairro'] = endereco.bairro if endereco.bairro is not None else 'N/A'
            custom_fields[
                'endereco_cidade'] = endereco.cidade if endereco.cidade is not None else 'N/A'
            custom_fields[
                'endereco_estado'] = endereco.estado if endereco.estado is not None else 'N/A'
            custom_fields[
                'endereco_numero'] = endereco.numero if endereco.numero is not None else 'N/A'
            custom_fields[
                'endereco_complemento'] = endereco.complemento if endereco.complemento is not None else 'N/A'
            custom_fields[
                'endereco_principal'] = endereco.principal if endereco.principal is not None else False
            custom_fields[
                'endereco_logradouro'] = endereco.logradouro if endereco.logradouro is not None else 'N/A'
        """Dados do responsável"""
        exists, responsavel = self.get_responsavel(lojista=lojista)
        if exists:
            custom_fields[
                'responsavel_nome'] = responsavel.nome if responsavel.nome is not None else 'N/A'
            custom_fields[
                'responsavel_sexo'] = responsavel.sexo if responsavel.sexo is not None else 'N/A'
            custom_fields[
                'responsavel_ocupacao'] = responsavel.ocupacao if responsavel.ocupacao is not None else 'N/A'
            custom_fields[
                'responsavel_documento'] = responsavel.documento if responsavel.documento is not None else 'N/A'
            custom_fields[
                'responsavel_profissao'] = responsavel.profissao if responsavel.profissao is not None else 'N/A'
            custom_fields[
                'responsavel_nacionalidade'] = responsavel.nacionalidade if responsavel.nacionalidade is not None else 'N/A'
            custom_fields[
                'responsavel_data_nascimento'] = responsavel.data_nascimento if responsavel.data_nascimento is not None else 'N/A'

            contact['email'] = responsavel.email
            payload['email'] = responsavel.email
            payload['name'] = responsavel.nome if responsavel.nome is not None else 'N/A'
            contact[
                'number'] = responsavel.ddd + responsavel.telefone if responsavel is not None else 'N/A'
            contact['type'] = 1
            contact['countryCode'] = "55"
            phone_contacts.append(contact)

        else:
            payload['name'] = lojista.nome if lojista.nome is not None else 'Nome do lojista'
            payload['email'] = 'N/A'
            custom_fields['responsavel_nome'] = 'N/A'
            custom_fields['responsavel_sexo'] = 'N/A'
            contact['email'] = 'N/A'
            custom_fields['responsavel_ocupacao'] = 'N/A'

        """Dados bancários"""
        exists, domicilio = self.get_dados_bancarios(lojista=lojista)
        if not exists:
            custom_fields['dados_bancarios_banco'] = 'N/A'
            custom_fields['dados_bancarios_conta'] = 'N/A'
            custom_fields['dados_bancarios_agencia'] = 'N/A'
            custom_fields['dados_bancarios_tipo_conta'] = 'N/A'
            custom_fields['dados_bancarios_digito_conta'] = 'N/A'
            custom_fields['dados_bancarios_nome_titular'] = 'N/A'
            custom_fields['dados_bancarios_documento_titular'] = 'N/A'
        else:
            custom_fields[
                'dados_bancarios_banco'] = domicilio.banco if domicilio.banco is not None else 'N/A'
            custom_fields['dados_bancarios_conta'] = str(
                domicilio.conta) if domicilio.conta is not None else 'N/A'
            custom_fields[
                'dados_bancarios_agencia'] = domicilio.agencia if domicilio.agencia is not None else 'N/A'
            custom_fields[
                'dados_bancarios_tipo_conta'] = domicilio.tipo_conta if domicilio.tipo_conta is not None else 'N/A'
            custom_fields[
                'dados_bancarios_digito_conta'] = domicilio.digito_conta if domicilio.digito_conta is not None else 'N/A'
            custom_fields[
                'dados_bancarios_tipo_documento_titular'] = "CPF" if domicilio.matriz_titular == "lucree_QSA_pf" else "CNPJ"
            custom_fields['dados_bancarios_nome_titular'] = domicilio.nome_titular
            custom_fields['dados_bancarios_documento_titular'] = domicilio.documento_titular
        custom_fields['link_pagamento'] = has_link_pagamento
        custom_fields['maquina_fisica'] = has_maquina_fisica
        payload['type'] = 2
        payload['custom_fields'] = custom_fields
        payload['contact'] = contact
        payload['phone_contacts'] = phone_contacts
        logging.info("Payload built successfully")
        return True, payload
    def send_or_put_to_octadesk(self, lojista: DashboardLojista) -> (bool, str):
        try:
            payload_builded, payload = self.build_payload(lojista=lojista)
            if not payload_builded:
                logging.error("Error building payload")
            octadesk_id = lojista.octadesk_id
            payload = {k if k != 'custom_fields' else 'customField': v for k, v
                       in payload.items()}
            if octadesk_id is None:

                octadesk_id = find_octadesk_user(merchant=payload,
                                                 octadesk_auth=self.octadesk_auth)
            if octadesk_id is not None:
                method = 'PUT'
                update_merchant(merchant=lojista, octadesk_id=octadesk_id)
            else:
                method = 'POST'

            octadesk_response = send_octadesk_webhook(
                payload_final=payload,
                octadesk_id=octadesk_id,
                octadesk_auth=self.octadesk_auth
            )
            if not octadesk_response.get('sent'):
                print(f"Error sending payload to octadesk. Method: {method}")
                logging.error(
                    f"Error sending payload to octadesk. Method: {method}")
                return False, "Error sending payload to octadesk"
            lojista.octadesk_id = octadesk_response.get('id')
            lojista.save()
            print("Enviado com sucesso")
            logging.info(
                f"Payload sent to octadesk successfully. Method: {method}")
            return True, "Payload sent to octadesk successfully"
        except Exception as e:
            logging.error(
                f"Error sending payload to octadesk. Method: {e}")
            return False, f"Error sending payload to octadesk {e}"

if __name__ == '__main__':
    octadesk = OctadeskMassive()
    qs = models_api.DashboardLojista.objects.exclude(produto__in=['Lucree Teste', 'Teste']).order_by('-data_cadastro')[2760:15000]
    contador: int = 0
    for lojista in qs:
        octadesk.send_or_put_to_octadesk(lojista=lojista)
        contador += 1
        print(f"Lojista {lojista.nome.upper()} sent to octadesk {contador}/{qs.count()}" )
