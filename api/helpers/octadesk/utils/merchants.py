from api.models.models import DashboardLojista
from api.helpers.octadesk.utils.logger import octadesk_logger


def update_merchant(merchant: DashboardLojista, octadesk_id: str) -> None: 
    try:
        merchant.octadesk_id = octadesk_id
        merchant.save()
    except Exception:
        octadesk_logger(lojista_id=merchant.id, octadesk_id=merchant.octadesk_id, method='PUT', payload={}, status_code=500, message="Falha ao salvar octadesk_id do lojista.")
