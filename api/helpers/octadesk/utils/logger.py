from api.models.octadesk import OctaLogs

def octadesk_logger(lojista_id, octadesk_id, method, payload, status_code, message):
    try:
        OctaLogs.objects.create(
            lojista_id=lojista_id,
            octadesk_id=octadesk_id,
            method=method,
            payload=payload,
            status_code=status_code,
            message=message
        )
    except Exception as error:
        print(f'Falha ao registrar logs: Schema: octadesk -> tabela: logs. Detalhes: {error}')