import json
import requests
from typing import Union
from credenciamento import settings
from api.models.octadesk import OctaSessions
from api.helpers.octadesk.utils.auth import renew_access_token


def send_octadesk_webhook(payload_final: dict, octadesk_auth: OctaSessions, octadesk_id = None) -> dict:
    url = settings.OCTADESK_PERSON_BASE_URL
    
    if url is None:
        raise ValueError("Variável de ambiente base do Octadesk está faltando.")

    integration_headers = { 
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + octadesk_auth.access_token
    }

    if octadesk_id is not None:
        url = f"{url}/{octadesk_id}"    
        response_final = requests.put(url=url, data=json.dumps(payload_final), headers=integration_headers)
        return {
            "id": None,
            "sent": True if response_final.status_code == 200 else False
        }

    response_final = requests.post(url=url, data=json.dumps(payload_final), headers=integration_headers)
    return {
        "id": response_final.json()["id"] if response_final.status_code == 200 else None,
        "sent": True if response_final.status_code == 200 else False
    } 


def find_octadesk_user(merchant: dict, octadesk_auth: OctaSessions) -> Union[str, None]:
    base_person_url = settings.OCTADESK_PERSON_BASE_URL
    if base_person_url is None:
        raise ValueError("Variável de ambiente base do Octadesk está faltando.")
    
    integration_headers = { 
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + octadesk_auth.access_token
    }

    merchant_document = merchant.get('customField').get('documento')
    merchant_email = merchant.get('email')
    merchant_id = merchant.get('customField').get('lojista_id')

    queries = [base_person_url + f"/?email={merchant_document}@lead.com.br", base_person_url + f"/?email={merchant_email}"]

    for query in queries:
        find_person_by_email = requests.get(query, headers=integration_headers)

        if find_person_by_email.status_code == 500:
            token_renew = renew_access_token(merchant_id=merchant_id, session=octadesk_auth)

            if not token_renew.get('renewed'):
                print('O token não renovado.')
                continue

            integration_headers["Authorization"] = f"Bearer {token_renew.get('token')}"
            find_person_by_email = requests.get(query, headers=integration_headers)
    
        if find_person_by_email.status_code == 200:
            return find_person_by_email.json()["id"]

    return None
