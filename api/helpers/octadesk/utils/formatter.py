def payload_formatter(lojista, hierarquia, responsavel, dados_bancarios, departamento, endereco_lojista) -> dict:
    
    payload_final = {}
    custom_fields = {}
    contact = {}
    phoneContacts = []

    payload_final["type"] = 2
    custom_fields["lojista_id"] = lojista.id
    custom_fields["user_id"] = lojista.lojista_id if lojista.lojista_id is not None else '0'
    payload_final["name"] = responsavel.nome if responsavel.nome is not None else 'Nome do lojista'
    custom_fields["razao_social"] = lojista.nome if lojista.nome is not None else 'Razao Social do Lojista'
    custom_fields["documento"] = lojista.documento
    custom_fields["num_terminais"] = lojista.num_terminais
    custom_fields["terminais_transacionando"] = 0   

    has_link_pagamento = False
    has_maquina_fisica = False

    descricaoDepartamento = 'N/A'
    if (lojista.departamento):
        descricaoDepartamento = departamento.descricao_departamento if departamento.descricao_departamento is not None else 'N/A'
    custom_fields["mcc"] = descricaoDepartamento if descricaoDepartamento is not None else 'N/A'

    custom_fields["fase_integracao"] = lojista.fase_integracao
    data_cadastro = lojista.data_cadastro
    data_update = lojista.data_update

    if data_cadastro == None and data_update == None:
        custom_fields["data_cadastro"] = None
    else:
        custom_fields["data_cadastro"] = lojista.data_cadastro.strftime("%Y-%m-%d") if lojista.data_cadastro is not None else lojista.data_update.strftime("%Y-%m-%d")

    if lojista.flag_link_pagto is not None and lojista.flag_link_pagto >= 3:
        has_link_pagamento = True

    custom_fields['agente_comercial_nome'] = hierarquia.usu_nome if hierarquia is not None else 'Não possui hierarquia'
    custom_fields['agente_comercial_email'] = lojista.email_vendedor_responsavel
    custom_fields['agente_comercial_cargo_hierarquia'] = hierarquia.cargo.descricao if hierarquia is not None else 'Não possui hierarquia'
    custom_fields['agente_comercial_projeto'] = lojista.produto
    custom_fields['agente_comercial_produto'] = lojista.produto

    custom_fields['endereco_cep'] = endereco_lojista.cep if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_bairro'] = endereco_lojista.bairro if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_cidade'] = endereco_lojista.cidade if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_estado'] = endereco_lojista.estado if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_numero'] = endereco_lojista.numero if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_complemento'] = endereco_lojista.complemento if endereco_lojista is not None else 'N/A'
    custom_fields['endereco_principal'] = endereco_lojista.principal if endereco_lojista is not None else False
    custom_fields['endereco_logradouro'] = endereco_lojista.logradouro if endereco_lojista is not None else 'N/A'

    if lojista.plano is not None:
        if hasattr(lojista, 'operadora'):
            custom_fields['operadora_id'] = lojista.operadora.id if lojista.operadora is not None else 'N/A'
            custom_fields['operadora_nome'] = lojista.operadora.nome if lojista.operadora is not None else 'N/A'
            custom_fields['operadora_identificador'] = lojista.operadora.identificador if lojista.operadora is not None else 'N/A'
            custom_fields['operadora_provedor'] = lojista.operadora.provedor if lojista.operadora is not None else 'N/A'
        else:
            custom_fields['operadora_id'] = 'N/A'
            custom_fields['operadora_nome'] = 'N/A'
            custom_fields['operadora_identificador'] = 'N/A'
            custom_fields['operadora_provedor'] = 'N/A'

        has_maquina_fisica = True

    if lojista.plano_virtual is not None:
        has_link_pagamento = True

    custom_fields['responsavel_nome'] = responsavel.nome if responsavel is not None else 'N/A'
    custom_fields['responsavel_sexo'] = responsavel.sexo if responsavel is not None else 'N/A'
    payload_final['email'] = responsavel.email if responsavel is not None else 'N/A'
    custom_fields['responsavel_ocupacao'] = responsavel.ocupacao if responsavel is not None else 'N/A'
    contact['number'] = responsavel.ddd + responsavel.telefone if responsavel is not None else 'N/A'
    contact['type'] = 1
    contact['countryCode'] = "55"
    phoneContacts.append(contact)
    custom_fields['responsavel_documento'] = responsavel.documento if responsavel is not None else 'N/A'
    custom_fields['responsavel_profissao'] = responsavel.profissao if responsavel is not None else 'N/A'
    custom_fields['responsavel_nacionalidade'] = responsavel.nacionalidade if responsavel is not None else 'N/A'
    custom_fields['responsavel_data_nascimento'] = responsavel.data_nascimento if responsavel is not None else 'N/A'

    if dados_bancarios is not None:
        custom_fields['dados_bancarios_banco'] = dados_bancarios.banco if dados_bancarios is not None else None
        custom_fields['dados_bancarios_conta'] = str(dados_bancarios.conta) if dados_bancarios is not None else None
        custom_fields['dados_bancarios_agencia'] = dados_bancarios.agencia if dados_bancarios is not None else None
        custom_fields['dados_bancarios_tipo_conta'] = dados_bancarios.tipo_conta if dados_bancarios is not None else None
        custom_fields['dados_bancarios_digito_conta'] = dados_bancarios.digito_conta if dados_bancarios is not None else None
        custom_fields['dados_bancarios_tipo_documento_titula'] = "CPF" if dados_bancarios.matriz_titular == "lucree_QSA_pf" else "CNPJ"
        custom_fields['dados_bancarios_nome_titular'] = dados_bancarios.nome_titular if dados_bancarios.nome_titular is not None else 'Nome do titular'
        custom_fields['dados_bancarios_documento_titular'] = dados_bancarios.documento_titular

    custom_fields['link_pagamento'] = has_link_pagamento
    custom_fields['maquina_fisica'] = has_maquina_fisica

    payload_final['customField'] = custom_fields
    payload_final['phoneContacts'] = phoneContacts    

    return payload_final