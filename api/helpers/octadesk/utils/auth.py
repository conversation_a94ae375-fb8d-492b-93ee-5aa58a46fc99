import requests

from typing import Union
from api.helpers.octadesk.utils.logger import octadesk_logger
from credenciamento import settings
from api.models.octadesk import OctaSessions

def renew_access_token(session: OctaSessions, merchant_id: int) -> str:
    url = settings.OCTADESK_AUTH_URL

    token_headers = { 
        'Content-Type': 'application/json',
        'subDomain': 'o158750-ec2',
        'username': '<EMAIL>',
        'apiToken': settings.OCTADESK_API_TOKEN
    }

    response = requests.post(url, headers=token_headers)
    if response.status_code != 200:
        octadesk_logger(lojista_id=merchant_id, octadesk_id=None, method='POST', payload={}, status_code=500, message="Não foi possível obter token de autenticação")
        return {
            "renewed": False,
            "token": None
        }
    try:
        token = response.json()["token"]
        session.access_token = token
        session.save()
        return {
            "renewed": True,
            "token": token
        }
    except Exception:
        octadesk_logger(lojista_id=merchant_id, octadesk_id=None, method='POST', payload={}, status_code=500, message="Não foi possível salvar token no banco de dados")
    
    return {
        "renewed": True,
        "token": token
    }
