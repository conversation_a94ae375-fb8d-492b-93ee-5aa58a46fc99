
from rest_framework import status
from api.models.octadesk import OctaSessions
from rest_framework.response import Response
from api.helpers.octadesk.utils.logger import octadesk_logger
from api.helpers.octadesk.utils.merchants import update_merchant
from api.helpers.octadesk.utils.formatter import payload_formatter
from api.helpers.octadesk.utils.requester import find_octadesk_user, send_octadesk_webhook
from api.models import DashboardLojista, DashboardDepartamentos, AuthUser, Hierarquia, DashboardEnderecolojista, DashboardResponsavel, DashboardDomiciliobancario

       
def post_octadesk_webhook(lojista_id: int):

    try:
        lojista = DashboardLojista.objects.get(pk=lojista_id)
        if lojista.produto == 'Lucree Teste' or lojista.produto == 'Teste':
            octadesk_logger(
                lojista_id=lojista_id, 
                octadesk_id=lojista.octadesk_id, 
                method="POST", 
                payload={}, 
                status_code=500, 
                message=f"Lojista com produto não permitido. Produto: {lojista.produto}"
            )
            return Response({ "message": f"Lojista com produto não permitido. Produto: {lojista.produto}" }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        departamento = DashboardDepartamentos.objects.filter(id=lojista.departamento).first()
        user = AuthUser.objects.get(email=lojista.email_vendedor_responsavel)
        hierarquia = Hierarquia.objects.filter(id_usu=user.id).first()
        responsavel = DashboardResponsavel.objects.filter(lojista=lojista).first()
        dados_bancarios = DashboardDomiciliobancario.objects.filter(lojista=lojista, ativo=True).first()
        endereco_lojista = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo='1').first()
        octadesk_auth = OctaSessions.objects.all().first()
    except DashboardLojista.DoesNotExist as e:
        octadesk_logger(lojista_id=lojista_id, octadesk_id=None, method='POST', payload={}, status_code=500, message="Lojista não encontrado")
        return Response({ "message": f"Lojista não encontrado" }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except AuthUser.DoesNotExist as e:
        octadesk_logger(lojista_id=lojista_id, octadesk_id=lojista.octadesk_id, method='POST', payload={}, status_code=500, message="Lojista sem usuário atrelado")
        return Response({ "message": f"Lojista sem usuário atrelado" }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except DashboardDomiciliobancario.DoesNotExist as e:
        octadesk_logger(lojista_id=lojista_id, octadesk_id=lojista.octadesk_id, method='POST', payload={}, status_code=500, message="Lojista sem domicilio bancário atrelado")
        return Response({ "message": f"Lojista sem domicilio bancário atrelado" }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    payload_final = payload_formatter(
            lojista=lojista, 
            hierarquia=hierarquia,
            responsavel=responsavel,
            dados_bancarios=dados_bancarios,
            departamento=departamento,
            endereco_lojista=endereco_lojista
        )

    method = 'POST'
    octadesk_id = lojista.octadesk_id
    if octadesk_id is None:
        octadesk_id = find_octadesk_user(merchant=payload_final, octadesk_auth=octadesk_auth)

    if octadesk_id is not None:
        method = 'PUT'
        update_merchant(merchant=lojista, octadesk_id=octadesk_id)

    octadesk_response = send_octadesk_webhook(
        payload_final=payload_final, 
        octadesk_id=octadesk_id,
        octadesk_auth=octadesk_auth
    )

    if not octadesk_response.get('sent'):
        octadesk_logger(
            lojista_id=lojista_id, 
            octadesk_id=lojista.octadesk_id, 
            method=method, 
            payload=payload_final, 
            status_code=500, 
            message="Houve um problema ao enviar lojista para o Octadesk, checar logs."
        )
        print(f'[WH] Houve um problema ao enviar lojista para o Octadesk, checar logs.')
        return Response({"message": "Problema ao enviar lojista para o Octadesk."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    if octadesk_response.get('id') is not None:
        lojista.octadesk_id = octadesk_response.get('id')
        lojista.save()

    print(f'[WH] Lojista {lojista.id} enviado com sucesso para o Octadesk.')
    octadesk_logger(
            lojista_id=lojista_id, 
            octadesk_id=lojista.octadesk_id, 
            method=method, 
            payload=payload_final, 
            status_code=200, 
            message="Webhook enviado com sucesso."
        )
    return Response({"message": "Webhook enviado com sucesso."}, status=status.HTTP_200_OK)
