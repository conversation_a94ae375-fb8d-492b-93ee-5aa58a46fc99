import logging
import json
from django.db.models import Q
from datetime import date, datetime
from django.db import transaction, IntegrityError

from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido
from api.helpers.consulta.validate_form import user_exist
from api.helpers.idwall.idwall import IdWall
from api.helpers.functions.funcs import Funcs, parse_name
from api.helpers.octadesk.send_webhook import post_octadesk_webhook
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.helpers.sankhya import Sankhya
from api.helpers.telegram import Telegram
from api.models import AuthUser, Profile, DashboardTaxas, DashboardLojista, \
    DashboardResponsavel, DashboardEnderecolojista, DashboardDistribuidores, \
    DashboardDomiciliobancario, Hierarquia, Cargo, Produto, Credenciamento, User, DashboardCessao, DashboardPagamento, \
    DashboardPlanos, DashboardAuditoriaPlanos, LimiteTransacional, DadosSocietariosEc, DashboardProjetos, \
    SistemaGeradorCredito
from django.contrib.auth.models import Permission
from api.helpers.pendentes import send_pendente
import os

from api.models.profile import ProdutosAdquiridos

logger = logging.getLogger(__name__)


def fase_inicial():
    return json.dumps([
        {"fase": "An\u00e1lise Id Wall", "status": "em_andamento", "dt_alteracao": "{}".format(str(datetime.now()))},
        {"fase": "Aprovado risco IDWALL", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Reprovado risco IDWALL", "status": "nao_considerar", "dt_alteracao": "", "data": ""},
        {"fase": "An\u00e1lise de documenta\u00e7\u00e3o", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o pendente", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o aprovada", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o reprovada", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "An\u00e1lise gest\u00e3o de risco", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Aprovado gest\u00e3o de risco", "status": "nao_considerar", "dt_alteracao": "", "data": ""},
        {"fase": "Reprovado gest\u00e3o de risco", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Integrado 4ward (aguardando retorno)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Problema na integra\u00e7\u00e3o 4ward", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Integrado ADIQ (MID e TID)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Integrado PHOEBUS (Token)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Em aprova\u00e7\u00e3o comercial", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Envio de m\u00e1quina negado", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Faturamento", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Faturamento pendente", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Prepara\u00e7\u00e3o da m\u00e1quina", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Expedi\u00e7\u00e3o", "status": "pendente", "dt_alteracao": ""},
        {"fase": "M\u00e1quina enviada", "status": "pendente", "dt_alteracao": ""},
        {"fase": "M\u00e1quina entregue", "status": "pendente", "dt_alteracao": ""}])


def fase_inicial_pagamento():
    return json.dumps([
        {"fase": "Pagamento em confirma\u00e7\u00e3o", "status": "em_andamento",
         "dt_alteracao": "{}".format(str(datetime.now()))},
        # {"fase": "Pagamento confirmado", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Pagamento n\u00e3o confirmado", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Pagamento pendente vendedor", "status": "pendente", "dt_alteracao": ""},
        {"fase": "An\u00e1lise inicial", "status": "pendente", "dt_alteracao": ""},
        {"fase": "An\u00e1lise Id Wall", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Aprovado risco IDWALL", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Reprovado risco IDWALL", "status": "nao_considerar", "dt_alteracao": "", "data": ""},
        {"fase": "An\u00e1lise de documenta\u00e7\u00e3o", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o pendente", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o aprovada", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Documenta\u00e7\u00e3o reprovada", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "An\u00e1lise gest\u00e3o de risco", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Aprovado gest\u00e3o de risco", "status": "nao_considerar", "dt_alteracao": "", "data": ""},
        {"fase": "Reprovado gest\u00e3o de risco", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Integrado 4ward (aguardando retorno)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Problema na integra\u00e7\u00e3o 4ward", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Integrado ADIQ (MID e TID)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Integrado PHOEBUS (Token)", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Em aprova\u00e7\u00e3o comercial", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Envio de m\u00e1quina negado", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Faturamento", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Faturamento pendente", "status": "nao_considerar", "dt_alteracao": ""},
        {"fase": "Prepara\u00e7\u00e3o da m\u00e1quina", "status": "pendente", "dt_alteracao": ""},
        {"fase": "Expedi\u00e7\u00e3o", "status": "pendente", "dt_alteracao": ""},
        {"fase": "M\u00e1quina enviada", "status": "pendente", "dt_alteracao": ""},
        {"fase": "M\u00e1quina entregue", "status": "pendente", "dt_alteracao": ""}
    ])


def build_user(form):
    try:
        r = form['lojista']['responsavel']
        first_name, last_name = parse_name(r["nome"])
        user = User()
        user.username = r['email'].lower().replace(" ", "")
        user.password = 'pbkdf2_sha256$150000$Wl0R9vH1a7gl$ncGijZyxQybdHxQZRV7ZDlGl9P83XiutjG9pQMEUonQ='
        user.email = r['email'].lower().replace(" ", "")
        # join o sobrenome restante
        # tipo [Maria, do, Rosario] retira o primeiro nome e soma os outros
        # max caracter 150
        user.first_name = first_name
        user.last_name = last_name

        user.is_active = True
        user.date_joined = datetime.now()
        user.is_superuser = False
        user.is_staff = False
        return user
    except KeyError as e:
        raise ValueError('erro ao recuperar a chave: {} - build_user'.format(e))
    except ValueError as e:
        raise ValueError('Nome e sobrenome invalido')


def build_profile(form, user_id, produto, distribuidor):
    profile = Profile()
    profile.user_id = user_id
    profile.distribuidor = distribuidor
    profile.tipo_projeto = produto
    profile.tipo_perfil = 'Cliente'
    profile.tipo_cargo_id = 8
    return profile


def build_taxa(form, plano, lojista):
    taxa = DashboardTaxas()
    taxa.credito_a_vista = plano.taxa_credito
    taxa.credito_parcelado_emissor = plano.taxa_parcelado
    taxa.debito = plano.taxa_debito
    taxa.parcelado_loja_dois_seis = plano.taxa_parcelado
    taxa.parcelado_loja_sete_doze = plano.taxa_parcelado
    taxa.lojista = lojista
    taxa.plano_cadastro = datetime.now()
    return taxa


def build_lojista(form, user):
    try:
        loj = form['lojista']
        lojista = DashboardLojista()
        lojista.documento = loj['documento']
        lojista.departamento = loj['ramo-atividade']
        lojista.num_terminais = form['quantidade_terminais']

        # LUC-801 - Renato Aloi - 28/08/2020
        if 'codigo_cliente' in loj:
            lojista.codigo_cliente = loj['codigo_cliente']
        #

        # REMOVENDO PROCESSING INICIAL DO PLANO - DANIEL LIMA DO NASCIMENTO 17/10/2021 - PRICING
        # if loj['plano']:
        #     lojista.plano_id = loj['plano']
        #     plano = DashboardPlanos.objects.get(pk=lojista.plano_id)
        #     # DANIEL LIMA DO NASCIMENTO - EXCESSÃO PARA PLANOS COM 18X SDESK-1890
        #     # 13/04/2021
        #     if plano.p18:
        #         lojista.plano_18x_id = loj['plano']
        #         lojista.flag_pagto_18x = 4
        # if loj['plano_virtual']:
        #     lojista.plano_virtual_id = loj['plano_virtual']
        #     lojista.flag_link_pagto = 1

        # lojista.tipo_contratacao = loj['tipo_contratacao']
        # lojista.operadora_id = loj['operadora']
        lojista.email_vendedor_responsavel = form['email_vendedor_responsavel']
        lojista.matriz_idwall = loj['matriz_lojista']
        lojista.origem = form['origem']
        lojista.produto = form['produto']
        lojista.cadastro_ativo = True
    except KeyError as e:
        raise ValueError('Dados inválidos no build de lojista: {}'.format(e))

    lojista.relatorio_a_partir_pj = False
    lojista.lojista_id = user.id

    idwall = IdWall()

    try:
        res = loj['responsavel']
        if lojista.matriz_idwall == 'lucree_QSA_pf':
            idwall_relatorio = idwall.create_relatorio(
                lojista.matriz_idwall, res['documento'], res['data_nascimento'])
        else:
            idwall_relatorio = idwall.create_relatorio(
                lojista.matriz_idwall, loj['documento'])

        idwall_numero = None
        if 'result' in idwall_relatorio:
            idwall_numero = idwall_relatorio['result']['numero']

        if 'error' in idwall_relatorio:
            send_pendente(lojista, "IDWALL", "envio", idwall_relatorio.get("message"))

    except KeyError as e:
        raise ValueError(
            'Dado inválido na criacao de relatorio IDWall: {}'.format(e))

    try:
        lojista.numero_idwall = idwall_numero
    except NameError:
        lojista.numero_idwall = None
    # if lojista.tipo_contratacao == 'VENDA':
    #     lojista.fase = fase_inicial_pagamento()
    #     lojista.fase_integracao = 'PAGAMENTO'
    #     lojista.area_repsonsavel = 'cadastro'
    # else:
    lojista.fase = fase_inicial()
    lojista.fase_integracao = 'IDWALL'
    lojista.area_repsonsavel = 'cadastro'

    if lojista.origem == 'Aplicativo':
        lojista.produto = 'Lucree Auto'
        lojista.banco_favorecido = 0

    else:

        try:
            # if lojista.tipo_contratacao and lojista.tipo_contratacao == 'VENDA':
            #     lojista.status_idwall = 'PENDENTE'
            # else:
            idwall_consulta = idwall.consulta_resultado(lojista.numero_idwall)
            if 'result' in idwall_consulta and 'status' in idwall_consulta:
                idwall_result = idwall_consulta['result']
                idwall_status = idwall_result['status']
                lojista.status_idwall = idwall_status
        except KeyError as e:
            raise ValueError(
                'Dados inválidos no resultado IDWall: {}'.format(e))

        lojista.data_cadastro = datetime.utcnow()

        if lojista.produto == 'CredMoura':

            try:
                distribuidor = DashboardDistribuidores.objects.get(
                    pk=loj['distribuidor'])
            except KeyError as e:
                raise ValueError(
                    'Dados inválidos na consulta de distribuidores: {}'
                    .format(e))

            lojista.agencia_favorecido = distribuidor.agencia
            lojista.banco_favorecido = distribuidor.banco
            lojista.conta_favorecido = distribuidor.conta
            lojista.digito_conta_favorecido = distribuidor.digito_conta
            lojista.tipo_conta_favorecido = distribuidor.tipo_conta
            lojista.cnpj_favorecido = distribuidor.cnpj
            lojista.nome_razao_favorecido = distribuidor.razao_social
            lojista.distribuidor = distribuidor

        # DANIEL ENVIANDO ALERTA QUANDO FOR CADASTRADO UM CLIENTE COM SPLIT (TEMPORÁRIO, APÓS VIRADA DO TARVOS ISSO NÃO SERÁ MAIS NECESSÁRIO)
        # REMOVENDO SPLIT ATIVO - DANIEL LIMA DO NASCIMENTO - 1710/2021 - PRICING
        # if lojista.plano.split_ativo:
        #     print(lojista.plano.split_favorecidos[0]['participacao'])
        #     mensagem: str = f"""
        #     Olá pessoal, um novo cliente foi credenciado com um plano que contém split, aqui vão os dados:\n\nDADOS DO CLIENTE:\n\nDOCUMENTO: {lojista.documento}\n\n---------------------------x----------------------\n\nDADOS DO SPLIT:\n\nPROJETO: {lojista.produto}\nPLANO: {lojista.plano.nome}\nPARTICIPANTE: {lojista.plano.split_favorecidos[0]['nome']}\nPARTICIPAÇÃO: {lojista.plano.split_favorecidos[0]['participacao']}%\n\n Após integração com o Skipper será preciso configurar a regra de split lá também
        #     """
        #     Telegram(
        #         token='**********************************************',
        #         chat_id='-529331885'
        #     ).send_message(mensagem)
        # DANIEL LIMA DO NASCIMENTO - EXCESSÃO PARA CAMALEÃO
        if lojista.produto == 'Camaleão':
            projeto = DashboardProjetos.objects.get(pk=23)
            SistemaGeradorCredito.objects.create(
                documento=lojista.documento,
                documento_gestor_credito='38368810000178',
                projeto=projeto
            )
            Sankhya(
                endpoint=os.environ.get("API_SANKHYA_ENDPOINT"),
                headers={
                    'x-api-key': os.environ.get("API_SANKHYA_KEY")
                },
                body={
                    "projectId": "camaleao",
                    "register": lojista.documento
                }
            ).send()
    return lojista


def build_manutencao_planos(lojista):
    auditoria_planos = DashboardAuditoriaPlanos()
    auditoria_planos.lojista = lojista
    if lojista.plano_id:
        auditoria_planos.plano = DashboardPlanos.objects.get(pk=lojista.plano_id)
    auditoria_planos.plano_antigo = None
    return auditoria_planos


def mount_responsavel_object(responsavel, lojista, form, update=False):
    r = form['lojista']['responsavel']
    responsavel.nome = r['nome']
    responsavel.documento = r['documento']
    responsavel.tipo = 10
    responsavel.data_nascimento = r['data_nascimento']
    responsavel.sexo = r['sexo']
    responsavel.ddd = r['ddd']
    responsavel.telefone = r['telefone']
    responsavel.email = r['email']
    responsavel.nacionalidade = r['nacionalidade']

    if not update:
        responsavel.lojista = lojista
    return responsavel


def build_responsavel(form, lojista):
    try:
        return mount_responsavel_object(DashboardResponsavel(), lojista, form)
    except KeyError as e:
        raise ValueError(
            'Dados inválidos no build de responsavel: {}'.format(e))


def mount_endereco_object(endereco, lojista, form, update=False):
    e = form['lojista']['endereco']
    endereco.principal = e['principal']
    endereco.cidade = e['cidade']
    endereco.estado = e['estado']
    endereco.numero = e['numero']
    endereco.cep = e['cep']
    endereco.complemento = e['complemento']
    endereco.logradouro = e['logradouro']
    endereco.bairro = e['bairro']
    endereco.tipo = '1'
    endereco.pessoas_endereco = ''

    if not update:
        endereco.utilizado_pedido = False
        endereco.data_cadastro = datetime.now()
        endereco.lojista = lojista
    return endereco


def build_endereco(form, lojista):
    try:
        return mount_endereco_object(DashboardEnderecolojista(), lojista, form)
    except KeyError as e:
        raise ValueError('Dados inválidos no build de endereco: {}'.format(e))


def build_domicilio_bancario(form, lojista):
    try:
        d = form['lojista']['dados_bancarios']
        domicilio_bancario = DashboardDomiciliobancario()
        domicilio_bancario.banco = d['banco']
        domicilio_bancario.agencia = d['agencia']
        domicilio_bancario.conta = d['conta']
        domicilio_bancario.digito_conta = d['digito_conta']
        domicilio_bancario.tipo_conta = d['tipo_conta']
        domicilio_bancario.lojista = lojista
        domicilio_bancario.data_cadastro = datetime.now()
        domicilio_bancario.documento_titular = d['documento_beneficiario']
        domicilio_bancario.nome_titular = d['nome_beneficiario']
        domicilio_bancario.ativo = True
        domicilio_bancario.matriz_titular = 'lucree_QSA_pj' if len(d['documento_beneficiario']) == 14 else 'lucree_QSA_pf'
        return domicilio_bancario
    except KeyError as e:
        raise ValueError(
            'Dados inválidos no build de domicilio bancario: {}'.format(e))


def build_pagamento(form, lojista):
    try:
        d = form['lojista']['dados_pagamento']
        pagamento = DashboardPagamento()
        pagamento.nsu = d['nsu']
        pagamento.data_pagamento = d['data_pagamento']
        pagamento.cod_autorizacao = d['cod_autorizacao']
        pagamento.valor = d['valor']
        pagamento.lojista = lojista
        pagamento.data_cadastro = datetime.now()
        return pagamento
    except KeyError as e:
        raise ValueError(
            'Dados inválidos no build de pagamento: {}'.format(e))


def build_opcionais(form, lojista):
    try:
        if 'opcionais' in form['lojista']:
            d = form['lojista']['opcionais']
            if 'socios' in d:
                if len(d['socios']):
                    for socio in d['socios']:
                        dados_societarios = DadosSocietariosEc()
                        dados_societarios.nome = socio['nome']
                        dados_societarios.documento = socio['documento']
                        dados_societarios.documento_tipo = socio['documentoTipo']
                        dados_societarios.nivel_societario_secundario = socio['nivelSecundario']
                        dados_societarios.lojista = lojista
                        dados_societarios.save()
                    return True
            else:
                responsavel = DashboardResponsavel.objects.get(lojista=lojista)
                responsavel.ocupacao = d['ocupacao']
                responsavel.profissao = d['profissao']
                responsavel.save()
        else:
            return None
    except KeyError as e:
        raise ValueError(
            'Dados inválidos no build de pagamento: {}'.format(e))


def build_cessao(form, arquivo, lojista, user):
    cessao = DashboardCessao()
    loj = form['lojista']

    try:
        # cessao.maquina_cessao = loj['tipo_contratacao']
        cessao.foto_documento = arquivo['documento']

        # foto_documento_domicilio_bancario
        # foto_documento
        # foto_documento_adesao
        # foto_documento_endereco

        if arquivo['tipo_arquivo'] == 'Domicílio Bancário':
            cessao.tipo_arquivo = 'foto_documento_domicilio_bancario'
        elif arquivo['tipo_arquivo'] == 'Conta de Energia' or arquivo['tipo_arquivo'] == 'Conta de Telefone':
            cessao.tipo_arquivo = 'foto_documento_domicilio_bancario'
        else:
            cessao.tipo_arquivo = arquivo['tipo_arquivo']

        cessao.tipo_produto = form['produto']
        cessao.lojista = lojista
        cessao.user_id = user.id
        return cessao
    except (KeyError, Exception) as e:
        raise ValueError('erro no build_cessao : {}'.format(e))


def build_hierarquia(form, user, lojista):
    operador = AuthUser.objects.values('id').filter(
        Q(username=lojista.email_vendedor_responsavel) |
        Q(email=lojista.email_vendedor_responsavel)).order_by('id').first()

    if operador is not None:
        operador_pk = operador['id']
    else:
        raise ValueError('Operador nao encontrado na tabela de usuários - build_hierarquia')

    try:
        hierarquia_operador_pk = Hierarquia.objects.values('id').get(
            id_usu=operador_pk)['id']
    except Hierarquia.DoesNotExist:
        raise ValueError('Hierarquia do operador nao encontrado na tabela de hierarquia - build_hierarquia')

    projeto = Produto.objects.filter(nome=form['produto']).first()
    hierarquia_cargo = Cargo.objects.filter(descricao='Cliente', produto_id=projeto.id).first()

    hierarquia = Hierarquia()
    hierarquia.id_usu = user.id
    hierarquia.usu_nome = '{} {}'.format(user.first_name, user.last_name)
    hierarquia.usu_parent = hierarquia_operador_pk
    hierarquia.cargo = hierarquia_cargo
    hierarquia.documento_cpf = lojista.documento if \
        lojista.matriz_idwall == 'lucree_QSA_pf' else ''
    hierarquia.documento_cnpj = lojista.documento if \
        lojista.matriz_idwall == 'lucree_QSA_pj' else ''
    hierarquia.razao_social = lojista.nome_razao_favorecido if \
        lojista.matriz_idwall == 'lucree_QSA_pj' else ''
    return hierarquia


def build_produtos_adquiridos(lojista: DashboardLojista, user: AuthUser, responsavel: DashboardResponsavel):
    """Build de produtos adquiridos:
    Atualmente vamos precisar saber quais os produtos adquiridos pelo cliente no momento que ele se cadastrar
    em nosso ambiente de adquirencia


    Args:
        lojista (DashboardLojista): Objeto do Lojista
        user (AuthUser): Objeto de autenticação
        responsavel (DashboardResponsavel): Objeto do contato do lojista
    """

    produtos_adquiridos = ProdutosAdquiridos.objects.filter(documento=lojista.documento).first()
    if produtos_adquiridos:
        produtos_adquiridos.produtos_contratados = {
           **produtos_adquiridos.produtos_contratados,
           "acquirance": True
        }
        produtos_adquiridos.cliente = lojista
        produtos_adquiridos.save()
    else:
        produtos_adquiridos = ProdutosAdquiridos()
        produtos_adquiridos.cliente = lojista
        produtos_adquiridos.produtos_contratados = {
            "wallet": False,
            "credits": False,
            "withdraw": False,
            "insurance": False,
            "acquirance": True
        }
        produtos_adquiridos.documento = lojista.documento
        produtos_adquiridos.email = user.email
        if len(lojista.documento) > 11:
            produtos_adquiridos.tipo = 'company'
        else:
            produtos_adquiridos.tipo = 'person'

        produtos_adquiridos.telefone = f'{responsavel.ddd}{responsavel.telefone}'
        produtos_adquiridos.created_at = datetime.now()
        produtos_adquiridos.save()




def credenciamento_get(lojista) -> dict:
    endereco = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo=1).first()
    endereco = endereco.__dict__
    del endereco["id"]
    del endereco["_state"]

    responsavel = DashboardResponsavel.objects.get(lojista=lojista)
    responsavel = responsavel.__dict__
    del responsavel["id"]
    del responsavel["_state"]

    return {
        "lojista": {
            "dados": {
                "documento": lojista.documento,
            },
            "endereco": endereco,
            "responsavel": responsavel
        }
    }


def credenciamento_update(lojista, form):
    # atualiza os campos do endereco
    endereco = DashboardEnderecolojista.objects.filter(lojista=lojista, tipo=1).first()
    endereco = mount_endereco_object(endereco, lojista, form, True)
    endereco.save()

    # atualiza os campos do reponsavel
    responsavel = DashboardResponsavel.objects.get(lojista=lojista)
    responsavel = mount_responsavel_object(responsavel, lojista, form, True)
    responsavel.save()

    if 'dados' in form['lojista']:
        if lojista.documento != form['lojista']['dados']['documento']:
            lojista.documento = form['lojista']['dados']['documento']
            lojista.save()


def credenciamento_post_process(cred_id):
    """
    Realiza o credenciamento completo em todas
    as 'subtabelas' dentro de uma transaction.
    Em caso de sucesso, o objeto credenciamento
    é retornado. Em caso de falha, a exception
    ValueError é levantada.
    """

    try:
        credenciamento = Credenciamento.objects.get(id=cred_id)
    except Credenciamento.DoesNotExist:
        logger.error("Credenciamento de id {} nao encontrado".format(cred_id))
        return

    form = credenciamento.formulario

    try:
        with transaction.atomic():

            lojista = form['lojista']
            user = User.objects.filter(username=lojista['responsavel']['email']).first()
            cria_profile = False
            if ( user is None):
                cria_profile = True
                user = build_user(form)
                user.save()
                # Adicionando permissões do usuário lojista Lucree
                # conforme tarefa SDESK-618 do Sprint 12
                permission_view_lojista = Permission.objects.filter(codename='view_lojista').first()
                user.user_permissions.add(permission_view_lojista)
                user.save()

            lojista = build_lojista(form, user)
            lojista.save()

            # REMOVENDO MANUTENÇÃO DE PLANOS DO PROCESSING INICIAL - DANIEL LIMA DO NASCIMENTO - 17/10/2021 - PRICING
            # manutencao_planos = build_manutencao_planos(lojista)
            # manutencao_planos.save()

            # REMOVENDO BUILD DE TAXAS DO PROCESSING INICIAL - DANIEL LIMA DO NASCIMENTO - 17/10/2021 - PRICING
            # if lojista.plano:
            #     taxa = build_taxa(form, lojista.plano, lojista)
            #     taxa.save()

            # if not lojista.plano_id and lojista.plano_virtual_id:
            #     lojista.num_terminais = 1

            endereco = build_endereco(form, lojista)
            endereco.save()

            responsavel = build_responsavel(form, lojista)
            responsavel.save()

            # build_opcionais(form, lojista)

            distribuidor = ''

            if form['produto'] != 'CredMoura':
                domicilio = build_domicilio_bancario(form, lojista)
                domicilio.save()
            else:
                distribuidor = lojista.cnpj_favorecido

            # elif form['produto'] == 'QueroPay':
            # if form['lojista']['plano']:
            #     plano = DashboardPlanos.objects.get(id=form['lojista']['plano'])
            #     if plano.tipo_contratacao == 'ALUGUEL':  # quando o plano for aluguel não deve cadastrar pagamento
            #         pass
            #     elif plano.tipo_contratacao == 'VENDA':
            #         a = DashboardPagamento.objects.filter(
            #             cod_autorizacao=form['lojista']['dados_pagamento']['cod_autorizacao'])
            #         if len(a) == 0:
            #             pagamento = build_pagamento(form, lojista)
            #             pagamento.save()
            #         else:
            #             raise Exception("Código de autorização já utilizado")

                # else:
                #    pass #futura chamada para cadastro no gateway

            if (cria_profile):
                profile = build_profile(
                        form,
                        user.id,
                        lojista.produto,
                        distribuidor)
                profile.save()

            arquivos = form['lojista']['arquivos']

            if arquivos:
                for arquivo in arquivos:
                    cessao = build_cessao(form, arquivo, lojista, user)
                    cessao.save()

            # if form['produto'] == 'Lucree Auto':
            #     hierarquia = build_hierarquia(form, user, lojista)
            #     hierarquia.save()

            # LUC-796 - Renato Aloi 17/08/2020
            # Adicionando cliente na tabela de limite transacional como 1 = REGRA GERAL
            lt = LimiteTransacional()
            lt.lojista_id = lojista.id
            lt.limite_id = 1  # fixando regra geral para cliente novo
            lt.save()

            # DANIEL NASCIMENTO 25/05/2022
            # adicionando processo para cadastros de produtos adquiridos
            build_produtos_adquiridos(lojista, user, responsavel)

            credenciamento.status = True
            credenciamento.observacao = 'Success'
            lojista.save()
            post_rdstation_webhook(lojista.id)
            post_octadesk_webhook(lojista.id)
            logger.debug("Post Process realizado com sucesso !")

    except Exception as e:
        credenciamento.observacao = e
        logger.error(
            'Falha no Post Process. Salvando observação: {}'.format(e))
        credenciamento.save()
        raise Exception(str(e))

    except (ValueError, KeyError, IntegrityError) as e:
        credenciamento.observacao = e
        logger.error(
            'Falha no Post Process. Salvando observação: {}'.format(e))
        credenciamento.save()
        raise ValueError

    return credenciamento


def pagamento_put_process(data, lojista):
    """
    Atualiza os dados de pagamento
    """
    try:
        with transaction.atomic():

            pagamentos = DashboardPagamento.objects.filter(lojista_id=lojista.id).values()

            pagamento = build_pagamento(data, lojista)
            pagamento.id = pagamentos[0]['id']
            pagamento.valor = pagamentos[0]['valor']
            pagamento.pagamento_valido = False
            pagamento.save()

            pagamento_comprovante = DashboardCessao.objects.get(lojista_id=lojista.id,
                                                                tipo_arquivo='comprovante_pagamento')
            pagamento_comprovante.foto_documento = data['lojista']['dados_pagamento']['comprovante']
            pagamento_comprovante.save()

            logger.debug("Put Pagamento realizado com sucesso !")

    except (ValueError, KeyError, IntegrityError, Exception) as e:
        logger.error(
            'Falha no Put Pagamento. Salvando observação: {}'.format(e))
        raise ValueError

    return pagamento


def pagamento_aprovacao_put_process(lojista):
    """
    Aprova o pagamento
    """
    result = {}
    try:
        with transaction.atomic():
            pagamento = DashboardPagamento.objects.filter(lojista_id=lojista.id).get()
            pagamento.pagamento_valido = True
            pagamento.save()

            sub_fase = Funcs.fase_atual(lojista.fase)
            lojista.fase = Funcs.atualiza_fase(
                fases_list=json.loads(lojista.fase),
                fases_concluidas=[sub_fase],
                proxima_fase='Análise inicial'
            )
            lojista.fase = Funcs.desabilita_fase(
                fases_list=lojista.fase,
                fases_desabilitar=['Pagamento em confirmação', 'Pagamento não confirmado',
                                   'Pagamento pendente vendedor']
            )
            responsavel = DashboardResponsavel.objects.get(lojista=lojista)
            data_nascimento = responsavel.data_nascimento
            lojista = Funcs.send_idwall(client=lojista, data_nascimento=data_nascimento)
            lojista.fase_integracao = 'IDWALL'
            lojista.save()

            logger.debug("Put Aprovação de Pagamento realizado com sucesso !")

    except (ValueError, KeyError, IntegrityError, Exception) as e:
        logger.error(
            'Falha no Put Aprovação de Pagamento: {}'.format(e))
        raise ValueError

    return pagamento
