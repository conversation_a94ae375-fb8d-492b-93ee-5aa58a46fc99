import logging
from api.helpers.idwall.idwall import IdWall
from api.helpers.paystore.pay_store import PayStore
from api.helpers.slack.Slack import Slack
from api.models import DashboardCpf, DashboardEnderecolojista, \
    DashboardEmailslojista, DashboardResumoconsultas, DashboardNomefonteconsulta, \
    DashboardTentativasresumoconsulta, DashboardDividaativa, DashboardProtesto, \
    DashboardValidacao, DashboardItemvalidacoes
from datetime import datetime

logger = logging.getLogger(__name__)


class IdwallProcessing:

    def __init__(self, protocolo, lojista):
        self.idwall = IdWall()
        self.consultas = self.idwall.relatorio_consulta(protocolo)
        self.validacoes = self.idwall.consulta_validacoes(protocolo)
        self.resultado = self.idwall.consulta_resultado(protocolo)
        self.lojista = lojista

    def valida_relatorios(self):

        try:
            self.consultas['result']
        except KeyError:
            raise ValueError('relatorio de consultas da idwall inválido! - build_save_consultas')

        try:
            self.resultado["result"]
        except KeyError:
            raise ValueError('relátorio de dados idwall inválido! - build_dividas_ativas')

        try:
            self.validacoes['result']
        except KeyError:
            raise ValueError('relátorio de validacões da idwall inválido! - build_validacao')

    def processing_pf(self):
        try:
            self.valida_relatorios()
            self.build_cpf()
            self.build_endereco()
            self.build_email()
            consultas = self.build_consultas()
            self.build_save_consultas(consultas)
            self.build_dividas_ativas()
            self.build_protestos()
            self.build_processamento_cpf()
            validacao = self.build_validacao()
            self.build_save_validacao(validacao)

        except ValueError:
            pass

    def processing_pj(self):
        pass

    def invertDate(self, data):
        date = datetime.strptime(data, '%Y-%m-%d')
        return date.strftime('%d-%m-%Y')

    def atualiza_fase(self, fases_list, fase_atual, proxima_fase):
        for fase in fases_list:
            if fase['fase'] == fase_atual:
                fase['status'] = 'concluida'
                fase['dt_alteracao'] = str(datetime.now())
            if fase['fase'] == proxima_fase:
                fase['status'] = 'em_andamento'
        return fases_list

    def fase_inicial(self):
        return [
            {"fase": "Em análise na Id Wall", "status": "em_andamento", "dt_alteracao": ""},
            {"fase": "Aprovado risco IDWALL", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Reprovado risco IDWALL", "status": "pentende", "dt_alteracao": "", "data": ""},
            {"fase": "Aprovado gestão de risco", "status": "pendente", "dt_alteracao": "", "data": ""},
            {"fase": "Reprovado gestão de risco", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Problema na integração 4ward", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Integrado 4ward (aguardando retorno)", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Integrado ADIQ (MID e TID)", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Integrado PHOEBUS (Token)", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Análise de documentação", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Documentação pendente", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Documentação aprovada", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Documentação reprovada", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Faturamento", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Faturamento pendente", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Preparação da máquina", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Expedição", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Máquina enviada", "status": "pendente", "dt_alteracao": ""},
            {"fase": "Máquina entregue", "status": "pendente", "dt_alteracao": ""}]

    def desabilita_fase(self, fases_list, fase):
        for fase in fases_list:
            if fase['fase'] == fase:
                fase['status'] = 'nao_considerar'
        return fases_list

    def build_mensagem(self):
        return {
            "attachments": [
                {
                    "fallback": "Nova avaliação de risco.",
                    "color": "#FF8C00",
                    "pretext": "Nova avaliação de risco",
                    "author_name": "Integrador Lucree",
                    "author_link": "http://integrador.lucree.com.br/dashboard/risc-details/1/",
                    "author_icon": "http://flickr.com/icons/bobby.jpg",
                    "title": '{} - {}'.format(self.lojista.nome, self.lojista.documento),
                    "title_link": 'http://integrador.lucree.com.br/dashboard/risc-details/{}/'.format(self.lojista.id),
                    "text": "Clique no link acima avaliar o novo cliente",
                    "fields": [
                        {
                            "title": "Prioridade",
                            "value": "Alta",
                            "short": False
                        },
                        {
                            "title": "Matriz",
                            "value": "Pessoa Física" if self.lojista.matriz_idwall == 'lucree_QSA_pf' else "Pessoa Jurídica",
                            "short": False
                        }
                    ],
                    "image_url": "http://my-website.com/path/to/image.jpg",
                    "thumb_url": "http://example.com/path/to/thumb.png",
                    "footer": "Análise Id Wall",
                    "footer_icon": "https://platform.slack-edge.com/img/default_application_icon.png",
                    "ts": 123456789
                }
            ]
        }

    def build_cpf(self):

        cpf = DashboardCpf()
        idwall_cpf = self.resultado['result']['cpf']

        cpf.sexo = idwall_cpf.get('sexo', '')
        cpf.numero = idwall_cpf.get('numero', '')
        cpf.data_de_nascimento = self.invertDate(idwall_cpf.get('data_de_nascimento', '1999-09-09'))
        cpf.nome = idwall_cpf.get('nome', '')
        cpf.renda = idwall_cpf.get('renda', '')
        cpf.pep = idwall_cpf.get('pep', '')
        cpf.situacao_imposto_de_renda = idwall_cpf.get('situacao_imposto_de_renda', '')
        cpf.cpf_situacao_cadastral = idwall_cpf.get('cpf_situacao_cadastral', '') or ""
        cpf.cpf_data_de_inscricao = self.invertDate(idwall_cpf.get('cpf_data_de_inscricao', '')) or""
        cpf.cpf_digito_verificador = idwall_cpf.get('cpf_digito_verificador', '') or ""
        cpf.cpf_anterior_1990 = idwall_cpf.get('cpf_anterior_1990', '') or ""
        cpf.ano_obito = idwall_cpf.get('ano_obito', '') or ""
        cpf.grafia = idwall_cpf.get('grafias', '') or ""
        cpf.lojista = self.lojista
        cpf.data_cadastro = datetime.now()
        cpf.data_update = datetime.now()
        cpf.save()

    def build_endereco(self):

        endereco = DashboardEnderecolojista()
        idwall_enderecos = self.resultado['result']['enderecos']

        for item in idwall_enderecos:
            if item['principal']:
                endereco.principal = item.get('principal', '')
                endereco.cidade = item.get('cidade', '')
                endereco.estado = item.get('estado', '')
                endereco.numero = item.get('numero', '')
                endereco.complemento = item.get('complemento', '')
                endereco.logradouro = item.get('logradouro', '')
                endereco.bairro = item.get('bairro', '')
                endereco.tipo = item.get('tipo', '')
                endereco.cep = item.get('cep', '')
                endereco.pessoas_endereco = item.get('pessoas_no_endereco', '')
                endereco.lojista = self.lojista
                endereco.data_cadastro = datetime.now()
                endereco.data_update = datetime.now()
                endereco.save()

    def build_email(self):

        try:
            emails = self.resultado['result']['emails']
        except KeyError:
            raise ValueError('erro ao recuperar a chave "emails" do relátorio de dados da idwall - build_email')

        for email in emails:
            mails = DashboardEmailslojista()
            try:
                mails.endereco = email['endereco']
            except KeyError:
                continue

            mails.lojista = self.lojista
            mails.data_cadastro = datetime.now()
            mails.data_update = datetime.now()
            mails.save()

    def build_consultas(self):

        consultas = self.consultas['result']

        try:
            status_protocolo = consultas['status_protocolo']
        except KeyError:
            raise ValueError('erro ao recuperar a chave: "status_protocolo" do relátorio de consultas da idwall - build_resumo_consultas')

        consulta = DashboardResumoconsultas()
        consulta.status_protocolo = status_protocolo
        consulta.nome_matriz = self.lojista.matriz_idwall
        consulta.lojista = self.lojista
        consulta.data_cadastro = datetime.now()
        consulta.data_update = datetime.now()
        consulta.save()

        return consulta

    def build_save_consultas(self, resumo_consultas):

        consultas = self.consultas['resultado']['consultas']

        for consulta in consultas:

            fonte = DashboardNomefonteconsulta()

            try:
                fonte.nome = consulta['nome']
                tentativas = consulta['tentativas']
            except KeyError:
                continue

            fonte.resumo_consulta = resumo_consultas
            fonte.data_cadastro = datetime.now()
            fonte.data_update = datetime.now()
            fonte.save()

            for tentativa in tentativas:

                tentativa = DashboardTentativasresumoconsulta()

                try:
                    tentativa.duracao_tentativa = tentativa["duracao_tentativa"]
                    tentativa.hora_fim_tentativa = tentativa["hora_fim_tentativa"]
                    tentativa.hora_inicio_tentativa = tentativa["hora_inicio_tentativa"]
                    tentativa.msg_erro_tentativa = tentativa["msg_erro_tentativa"]
                    tentativa.fonte = fonte
                    tentativa.status_fonte = tentativa["status_fonte"]
                    tentativa.status_tentativa = tentativa["status_tentativa"]
                    tentativa.tipo_erro_tentativa = tentativa["tipo_erro_tentativa"]
                except KeyError:
                    continue

                tentativa.save()

    def build_dividas_ativas(self):

        resultado = self.resultado['result']

        try:
            divida_ativa = resultado['divida_ativa']
        except KeyError:
            raise ValueError('erro ao recuperar a chave "divida_ativa" do relátorio de dados da idwall - build_dividas_ativas')

        if not divida_ativa:
            return

        divida = DashboardDividaativa()
        divida.lojista = self.lojista
        divida.data_cadastro = datetime.now()
        divida.data_update = datetime.now()

        try:
            divida.nome = divida_ativa['nome']
            divida.valor_devido = divida_ativa['valor_devido']
        except KeyError:
            divida.nome = 'idwall não informou'
            divida.valor_devido = 'idwall não informou'

        divida.save()

    def build_protestos(self):

        try:
            protestos = self.resultado["result"]["protestos"]
        except KeyError:
            raise ValueError('erro ao recuperar a chave "protestos" do relátorio de dados da idwall - build_protestos')

        if not protestos:
            return

        for item in protestos:
            protesto = DashboardProtesto()
            protesto.estado_protesto = protesto
            protesto.lojista = self.lojista
            protesto.save()

    def build_processamento_cpf(self):

        resultado = self.resultado['result']

        try:

            self.lojista.mensagem_idwall = resultado["mensagem"]
            self.lojista.status_idwall = resultado["status"]
            self.lojista.resultado_idwall = resultado["resultado"]
            self.lojista.pep_idwall = resultado['pep'].get('pep', False)
            self.lojista.nome = resultado["cpf"]["nome"]

        except KeyError as e:
            raise ValueError('Erro ao recuperar a chave: {} - build_processamento_cpf'.format(e))

        if resultado["resultado"] == "VALID":
            self.lojista.fase = self.atualiza_fase(
                self.lojista.fase,
                'Em análise na Id Wall',
                'Aprovado risco IDWALL')

            # skipper_integration(pesquisa_lojista.id)
            self.lojista.fase_integracao = "4WARD"

        else:

            self.lojista.fase = self.atualiza_fase(
                self.lojista.fase,
                'Em análise na Id Wall',
                'Reprovado risco IDWALL')

            self.lojista.fase = self.desabilita_fase(
                self.lojista.fase,
                'Aprovado risco IDWALL')

            self.lojista.mensagem_integrador = 'Aguarde o setor de risco analisar este cliente'
            self.lojista.com_protesto = True if 'estado_com_protestos' in resultado['protestos'] else False

            PayStore.py_mail(
                "Nova análise de risco",
                PayStore.mountEmailRisco(self.lojista.documento, self.lojista.nome),
                "<EMAIL>",
                "<EMAIL>")

            mensagem = self.build_mensagem
            Slack.alerta_risco(mensagem)

        self.lojista.data_update = datetime.now()
        self.lojista.save()

    def build_validacao(self):

        validacao = DashboardValidacao()
        validacoes = self.validacoes['result']

        try:

            validacao.mensagem = validacoes['mensagem']
            validacao.nome = validacoes['nome']
            validacao.resultado = validacoes['resultado']
            validacao.status = validacoes['status']

        except KeyError as e:
            raise ValueError('erro ao recuperar a chave: {} - build_validacao'.format(e))

        validacao.lojista = self.lojista
        validacao.data_update = datetime.now()
        validacao.save()

    def build_save_validacao(self, validacao):

        validacoes = self.validacoes['result']

        for item in validacoes:

            item_validacao = DashboardItemvalidacoes()

            item_validacao.regra = item.get('regra', None)
            item_validacao.nome = item.get('nome', None)
            item_validacao.descricao = item.get('descricao', None)
            item_validacao.resultado = item.get('resultado', None)
            item_validacao.mensagem = item.get('mensagem', None)
            item_validacao.validacao = validacao
            item_validacao.data_cadastro = datetime.now()
            item_validacao.data_update = datetime.now()
            item_validacao.save()
