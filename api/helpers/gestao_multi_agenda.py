from api.models import DashboardLojista


class GestaoMultiAgenda:
    def __init__(self):
        pass

    def busca_cliente_por_id(self, cliente_id: int):
        try:
            cliente = DashboardLojista.objects.get(pk=cliente_id)
            return cliente, True, None
        except DashboardLojista.DoesNotExist as e:
            return None, <PERSON>als<PERSON>, "Cliente não existe"
        except Exception as e:
            return None, Fals<PERSON>, "Erro inesperado"

    def altera_multiagenda_cliente(self, cliente: DashboardLojista, status: bool):
        cliente.multi_agenda = status
        cliente.save()

