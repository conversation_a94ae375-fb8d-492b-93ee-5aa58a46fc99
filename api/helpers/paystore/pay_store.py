from credenciamento import settings
from email.mime.multipart import MIMEM<PERSON>ipart
from email.mime.text import MIMEText
from django.template import loader
import smtplib


class PayStore:

    def __init__(self):
        self.headers = {
            'Content-Type': "application/json",
            'Authorization': "Bearer " + settings.PAYSTORE_TOKEN,
        }

    @classmethod
    def py_mail(self, SUBJECT, BODY, TO, FROM):
        # Create message container - the correct MIME type is multipart/alternative here!
        MESSAGE = MIMEMultipart('alternative')
        MESSAGE['subject'] = SUBJECT
        MESSAGE['To'] = TO
        MESSAGE['From'] = FROM

        MESSAGE.preamble = """
        Your mail reader does not support the report format."""

        # Record the MIME type text/html.
        HTML_BODY = MIMEText(BODY, 'html')

        # Attach parts into message container.
        # According to RFC 2046, the last part of a multipart message, in this case
        # the HTML message, is best and preferred.
        MESSAGE.attach(HTML_BODY)

        # The actual sending of the e-mail
        server = smtplib.SMTP('email-smtp.us-west-2.amazonaws.com:587')

        # Print debugging output when testing
        if __name__ == "__main__":
            server.set_debuglevel(1)

        # Credentials (if needed) for sending the mail
        login = 'AKIAIFOZGG6XVC3XSB7Q'
        password = "AqJf/MBKXpNqj+ON1H4BkBLHNqXK0X5AEYTJNAbYke0b"

        server.starttls()
        server.login(login, password)
        server.sendmail(FROM, [TO], MESSAGE.as_string())
        server.quit()

    @classmethod
    def mountEmailRisco(self, documento=None, nome_cliente=None):
        template = loader.get_template('dashboard/mail/alerta_risco_email.html')
        content = template.render({
            'documento': documento,
            'nome_razao_social': nome_cliente,
            'rota': 'rotaaaa'
        })

        return content
