from api.models.tokens import Tokens


class Auth:
    @staticmethod
    def have_custom_token(headers):
        if 'HTTP_CUSTOM_TOKEN' not in headers:
            return False
        return True

    @staticmethod
    def is_authenticated(custom_token):
        try:
            Tokens.objects.get(custom_token=custom_token)
            return True
        except Tokens.DoesNotExist:
            return False