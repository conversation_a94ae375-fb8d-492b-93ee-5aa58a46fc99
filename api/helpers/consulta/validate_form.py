import logging
from api.models import User, DashboardLojista, OnboardingV2
from api.helpers.cred_exceptions import UserAlreadyExist, LojistaAlreadyExist

logger = logging.getLogger(__name__)


def user_exist(email):
    try:
        User.objects.get(username=email)
        return True
    except User.DoesNotExist:
        return False


# LUC-801 - <PERSON><PERSON> - 22/08/2020
def codigo_cliente_exist(codigo_cliente):
    try:
        DashboardLojista.objects.get(codigo_cliente=codigo_cliente)
        return True
    except DashboardLojista.DoesNotExist:
        return False


def lojista_exist(documento):
    try:
        DashboardLojista.objects.get(documento=documento)
        return True
    except DashboardLojista.DoesNotExist:
        return False

def lojista_exist_in_onboarding_v2(documento: str):
    try:
        OnboardingV2.objects.get(documento=documento)
        return True
    except OnboardingV2.DoesNotExist:
        return False

def validate_form(data):
    try:
        lojista = data['formulario']['lojista']
        if lojista_exist(lojista['documento']):
            raise LojistaAlreadyExist
    except KeyError as e:
        logger.error('Chave não encontrada no formulario: {}'.format(e))
        raise KeyError


def validate_form_app(email: str, document: str):
    try:
        if user_exist(email):
            raise UserAlreadyExist
        if lojista_exist(document):
            raise LojistaAlreadyExist
    except KeyError as e:
        logger.error('Chave não encontrada no request: {}'.format(e))
        raise KeyError

def validate_type_client_app(document:str):
    if len(document) == 14:
        return 'company'
    elif len(document) == 11:
        return 'person'
    else:
        logger.error('Documento com o tamanho errado')
        raise KeyError
