import json
import requests


class HierarquiaLucree:
    # para validação
    endpoint = 'https://api-hml.lucree.com.br/'
    ws_key = '83fdc1e0-5fa5-3ebb-6ebc-1d768078fb6c'
    token = ''
    headers = {}

    def __init__(self):
        self.token = self.get_token()
        self.headers = {
            'Authorization': self.token
        }

    def get_token(self):
        headers = {
            'Content-Type': "application/json"
        }
        body = {
            "username": "daniel.nascimento",
            "password": "acessoLucree#2018"
        }

        results = requests.post(self.endpoint + 'autenticacao/v1', headers=headers, data=json.dumps(body))
        retorno = results.json()
        return retorno['id_token']

    def get_products(self):
        results = requests.request("GET", self.endpoint + 'hierarquia/produto', headers=self.headers)
        return results.json()

    def get_product(self, id_produto):
        results = requests.request("GET", self.endpoint + 'hierarquia/produto/'+str(id_produto), headers=self.headers)
        return results.json()

    def get_hierarquia_by_product(self, id_produto):
        results = requests.request("GET", self.endpoint + 'hierarquia/hierarquia-prod/'+str(id_produto), headers=self.headers)
        return results.json()

    def get_organizacao(self, id_usu):
        results = requests.request("GET", self.endpoint + 'hierarquia/organizacao/'+str(id_usu), headers=self.headers)
        return results.json()

    def get_cargos(self):
        results = requests.request("GET", self.endpoint + 'hierarquia/cargo', headers=self.headers)
        return results.json()

    def get_cargo(self, id_cargo):
        results = requests.request("GET", self.endpoint + 'hierarquia/cargo/'+str(id_cargo), headers=self.headers)
        return results

    def altera_cargos(self, id_cargo, data):
        results = requests.put(self.endpoint + 'hierarquia/cargo/'+str(id_cargo), data=data, headers=self.headers)
        return results.json()

    def cadastra_cargos(self, data):
        results = requests.post(self.endpoint + 'hierarquia/cargo', data=data, headers=self.headers)
        return results.json()

    def deleta_cargos(self, id_cargo):
        try:
            requests.delete(self.endpoint + 'hierarquia/cargo/'+str(id_cargo), headers=self.headers)
            return True
        except:
            return False

    def cadastra_item_hierarquia(self, data):
        results = requests.post(self.endpoint + 'hierarquia/hierarquia', data=data, headers=self.headers)
        return results.json()

    def get_itens_hierarquicos(self):
        results = requests.request("GET", self.endpoint + 'hierarquia/hierarquia', headers=self.headers)
        return results.json()

    def get_item_hierarquico(self, id_cliente):
        results = requests.request("GET", self.endpoint + 'hierarquia/hierarquia/' + str(id_cliente), headers=self.headers)
        return results

    def altera_item_hierarquia(self, id_hierarquia, data):
        results = requests.put(self.endpoint + 'hierarquia/hierarquia/' + str(id_hierarquia), data=data, headers=self.headers)

        return results

    def get_id_filhos(self, id_cliente):
        results = requests.request("GET", self.endpoint + 'hierarquia/hierarquia/filhos/' + str(id_cliente), headers=self.headers)
        return results

    def get_hierarquia_by_user(self, id_user):
        results = requests.request("GET", self.endpoint + 'hierarquia/hierarquia/by-user/' + str(id_user),
                                   headers=self.headers)
        return results