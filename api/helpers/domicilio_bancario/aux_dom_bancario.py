import logging
from datetime import datetime
from django.db import transaction, IntegrityError

from api.helpers.custom_exceptions.cerc import CercException
from api.helpers.octadesk.send_webhook import post_octadesk_webhook
from api.helpers.cerc.cerc_engine import envio_cerc_engine_sem_metodo_definido
from api.helpers.rdstation.send_webhook import post_rdstation_webhook
from api.models import DashboardDomiciliobancario, DashboardBancos, AuditoriaAlteracaoDomicilioBancario, \
    DashboardLojista, AllowContaTerceiros, AuditoriaContaTerceiros, DashboardDomiciliobancarioAux
from datetime import datetime
from rest_framework import status
from api.helpers.cerc.cerc_engine import envio_cerc_engine


logger = logging.getLogger(__name__)


def get_matriz_documento(tipo):
    if int(tipo) == 0:
        return 'lucree_QSA_pf'
    return 'lucree_QSA_pj'

def reformata_documento(documento: str):
    newdoc: str = documento.replace('.', '').replace('-', '').replace('/', '')
    return newdoc

def get_index_tipo_documento(matriz):
    if matriz.lower() == 'lucree_qsa_pf':
        return 0
    return 1

def get_conta_terceiro(documento):
    return AllowContaTerceiros.objects.filter(documento=documento).count()

def set_conta_terceiro(flag_conta_terceiro, cliente, usuario_responsavel, local_alteracao):
    if flag_conta_terceiro:
        count_conta = AllowContaTerceiros.objects.filter(documento=cliente.documento).count()
        if not count_conta:
            AllowContaTerceiros.objects.create(documento=cliente.documento)
            set_auditoria_conta_terceiro(usuario_responsavel, local_alteracao, cliente, 'INSERT')
    else:
        count_conta = AllowContaTerceiros.objects.filter(documento=cliente.documento).count()
        if count_conta:
            AllowContaTerceiros.objects.filter(documento=cliente.documento).delete()
            set_auditoria_conta_terceiro(usuario_responsavel, local_alteracao, cliente, 'REMOVE')

def set_auditoria_conta_terceiro(responsavel, local, lojista, tipo_alteracao):
    AuditoriaContaTerceiros.objects.create(
        responsavel=responsavel,
        tipo_alteracao=tipo_alteracao,
        local_alteracao=local,
        cliente=lojista
    )

def atualiza_dom_bancario(data, responsible_user, lojista, local_alteracao=None):
    filters = {
        "lojista": data.get("lojista_id"),
        "ativo": True
    }

    data_alteracao = datetime.now()

    lojista_id = filters["lojista"]
    # se for atualizar um domicilio especifico
    if "domicilio_id" in data:
        del filters["ativo"]
        filters["id"] = data["domicilio_id"]
    else:
        filters["lojista_id"] = filters["lojista"]
        del filters["lojista"]

    domBancario = DashboardDomiciliobancario.objects.filter(**filters).first()
    if data["ativo"]:
        DashboardDomiciliobancario.objects.filter(**filters).update(ativo=False)
    try:
        domBancario.banco = data['banco']
        domBancario.agencia = data['agencia']
        domBancario.conta = data['conta']
        domBancario.digito_conta = data['digito_conta']
        domBancario.tipo_conta = data['tipo_conta']
        domBancario.documento_titular = reformata_documento(data['documento_titular'])
        domBancario.nome_titular = str(data['nome_titular']).upper()
        # A PARTIR DO 01/09 O CAMPO CONTA_TERCEIRO NÃO É MAIS CONSIDERADO PARA IDENTIFICAR SE A CONTA POSSUÍ ESTA CARACTERISTICA OU NÃO
        # domBancario.conta_terceiro = data['conta_terceiro']
        domBancario.matriz_titular = data["matriz_titular"] if local_alteracao != 'Salesforce' else get_matriz_documento(data["tipo_documento"])
        domBancario.ativo = data['ativo']
        domBancario.data_update = data_alteracao
        domBancario.save()

        #FLUXO PARA CADASTRO DE DOMICÍLIOS DE TERCEIRO
        set_conta_terceiro(data['conta_terceiro'] if 'conta_terceiro' in data else False, lojista, responsible_user, local_alteracao)

        envio_cerc_engine(str(lojista_id), 'PUT')

        post_rdstation_webhook(lojista.id)
        post_octadesk_webhook(lojista.id)


        cadastra_auditoria(
            data=data,
            acao='UPDATE',
            status='SUCCESS',
            responsavel=responsible_user,
            data_alteracao=data_alteracao,
            lojista=lojista,
            local=local_alteracao if local_alteracao else None
        )

    except (ValueError, KeyError, IntegrityError, CercException, Exception) as e:
        # SALVANDO RETORNO NO LOG DA APLICAÇÃO
        msg = 'Falha no Put domicilio. Salvando observação: {}'.format(e)
        logger.error(msg)
        print(f"falha no domicilio. Obs: {msg}. Error: {e}")

        # IDENTIFICANDO SE O PROBLEMA ESTÁ NA CERC
        if type(e).__name__ == 'CercException':
            # PRIMEIRO REGISTRO NA AUDITORIA: SALVA O QUE ACONTECEU COM A CERC
            cadastra_auditoria(
                data=data,
                acao='UPDATE_CERC',
                status=msg,
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )
            # SEGUNDO REGISTRO NA AUDITORIA: APESAR DO PROBLEMA NA CERC A ATUALIZAÇÃO OCORREU LOCALMENTE
            cadastra_auditoria(
                data=data,
                acao='UPDATE',
                status='SUCCESS',
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )

            # CASO O ERRO SEJA DA CERC DEVEMOS CONTINUAR O PROCESSO E NÃO EXIBIR ERRO, POIS ESTAMOS COM INCONSISTÊNCIA DE DADOS
            return domBancario
        else:
            cadastra_auditoria(
                data=data,
                acao='UPDATE',
                status=msg,
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )
        raise Exception(msg)

    return domBancario

def atualiza_dom_bancario_aux(data, responsible_user, lojista, local_alteracao=None):

    data_alteracao = datetime.now()

    domBancario = DashboardDomiciliobancarioAux()

    try:
        domBancario.banco = data['banco']
        domBancario.agencia = data['agencia']
        domBancario.conta = data['conta']
        domBancario.domicilio = DashboardDomiciliobancario.objects.get(pk=data["domicilio_id"])
        domBancario.digito_conta = data['digito_conta']
        domBancario.tipo_conta = data['tipo_conta']
        domBancario.lojista = lojista
        domBancario.documento_titular = reformata_documento(data['documento_titular'])
        domBancario.nome_titular = str(data['nome_titular']).upper()
        domBancario.matriz_titular = data["matriz_titular"] if local_alteracao != 'Salesforce' else get_matriz_documento(data["tipo_documento"])
        domBancario.ativo = data['ativo']
        domBancario.responsavel = responsible_user
        domBancario.data_cadastro = datetime.now()
        domBancario.save()

        cadastra_auditoria(
            data=data,
            acao='UPDATE',
            status='SUCCESS',
            responsavel=responsible_user,
            data_alteracao=data_alteracao,
            lojista=lojista,
            local=local_alteracao if local_alteracao else None
        )

    except (ValueError, KeyError, IntegrityError, CercException, Exception) as e:
        # SALVANDO RETORNO NO LOG DA APLICAÇÃO
        msg = 'Falha no Put domicilio. Salvando observação: {}'.format(e)
        logger.error(msg)

        # IDENTIFICANDO SE O PROBLEMA ESTÁ NA CERC
        if type(e).__name__ == 'CercException':
            # PRIMEIRO REGISTRO NA AUDITORIA: SALVA O QUE ACONTECEU COM A CERC
            cadastra_auditoria(
                data=data,
                acao='UPDATE_CERC',
                status=msg,
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )
            # SEGUNDO REGISTRO NA AUDITORIA: APESAR DO PROBLEMA NA CERC A ATUALIZAÇÃO OCORREU LOCALMENTE
            cadastra_auditoria(
                data=data,
                acao='UPDATE',
                status='SUCCESS',
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )

            # CASO O ERRO SEJA DA CERC DEVEMOS CONTINUAR O PROCESSO E NÃO EXIBIR ERRO, POIS ESTAMOS COM INCONSISTÊNCIA DE DADOS
            return domBancario
        else:
            cadastra_auditoria(
                data=data,
                acao='UPDATE',
                status=msg,
                responsavel=responsible_user,
                data_alteracao=data_alteracao,
                lojista=lojista,
                local=local_alteracao if local_alteracao else None
            )
        raise Exception(msg)

    return domBancario

def cadastra_auditoria(data: dict, acao: str, status: str, responsavel: str, data_alteracao: datetime, lojista: DashboardLojista, local:str=None):
    auditoria = AuditoriaAlteracaoDomicilioBancario()
    auditoria.cliente = lojista
    auditoria.responsavel = responsavel
    auditoria.acao = acao.upper()
    auditoria.local_alteracao = ''
    auditoria.banco = data.get('banco', None)
    auditoria.conta = data.get('conta', None)
    auditoria.digito_conta = data.get('digito_conta', None)
    auditoria.agencia = data.get('agencia', None)
    auditoria.tipo_conta = data.get('tipo_conta', None)
    auditoria.nome_titular = data.get('nome_titular', None)
    auditoria.documento_titular = reformata_documento(data.get('documento_titular'))
    auditoria.resultado_alteracao = status.upper()
    auditoria.local_alteracao = local.upper() if local else 'SGC'
    auditoria.data_alteracao = data_alteracao
    auditoria.save()


def cadastra_novo_dom_bancario(data: dict, responsible_user, lojista, local_alteracao=None):
    data_cadastro = datetime.now()
    try:
        form = {
            "banco": data['banco'],
            "agencia": data['agencia'],
            "conta": data['conta'],
            "digito_conta": data['digito_conta'],
            "tipo_conta": data['tipo_conta'],
            "data_cadastro": data_cadastro,
            "ativo": data.get('ativo', False),
            "documento_titular": reformata_documento(data['documento_titular']),
            "nome_titular": str(data['nome_titular']).upper(),
            "matriz_titular": get_matriz_documento(data["tipo_documento"]),
            "lojista_id": data['lojista']
        }

        dom_bancarios = DashboardDomiciliobancario.objects.filter(lojista_id=data['lojista'])
        if form.get("ativo"):
            for dom in dom_bancarios:
                dom.ativo = False
                dom.save()

        dom_bancario = DashboardDomiciliobancario.objects.create(**form)

        envio_cerc_engine(str(data['lojista']), 'PUT')

        post_rdstation_webhook(lojista.id)
        post_octadesk_webhook(lojista.id)


        cadastra_auditoria(
            data=form,
            acao='CREATE',
            status='SUCCESS',
            responsavel=responsible_user,
            data_alteracao=data_cadastro,
            lojista=lojista,
            local=local_alteracao if local_alteracao else None
        )

        return dom_bancario
    except (ValueError, KeyError, IntegrityError, Exception) as e:
        msg = 'Falha no Post domicilio. Salvando observação: {}'.format(e)
        logger.error(msg)

        form = {
            "banco": data['banco'],
            "agencia": data['agencia'],
            "conta": data['conta'],
            "digito_conta": data['digito_conta'],
            "tipo_conta": data['tipo_conta'],
            "data_cadastro": data_cadastro,
            "conta_terceiro": data.get('conta_terceiro', False),
            "ativo": data.get('ativo', False),
            "documento_titular": reformata_documento(data['documento_titular']),
            "nome_titular": str(data['nome_titular']).upper(),
            "matriz_titular": get_matriz_documento(data["tipo_documento"]),
            "lojista_id": data['lojista']
        }

        cadastra_auditoria(
            data=form,
            acao='CREATE',
            status=msg,
            responsavel=responsible_user,
            data_alteracao=data_cadastro,
            lojista=lojista,
            local=local_alteracao if local_alteracao else None
        )

        raise Exception(msg)
