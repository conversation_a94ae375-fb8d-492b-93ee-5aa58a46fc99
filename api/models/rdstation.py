from django.db import models


class Sessions(models.Model):
    access_token = models.Char<PERSON><PERSON>(max_length=2000)
    refresh_token = models.Char<PERSON><PERSON>(max_length=2000)
    created_at = models.DateField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = '"rdstation\".\"sessions"'


class Logs(models.Model):
    lojista_id = models.IntegerField(null=False)
    rdstation_id = models.CharField(max_length=40, null=True)
    method = models.CharField(max_length=10, null=False)
    payload = models.JSONField()
    status_code = models.IntegerField(null=False)
    message = models.Char<PERSON><PERSON>(max_length=1000, null=False)
    created_at = models.DateTimeField(null=False, auto_now_add=True)

    class Meta:
        managed = False
        db_table = '"rdstation\".\"logs"'
