from django.db import models


class OctaSessions(models.Model):
    access_token = models.<PERSON><PERSON><PERSON><PERSON>(max_length=2000)
    created_at = models.DateField(auto_now_add=True)
    created_at = models.DateField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = '"octadesk\".\"sessions"'


class OctaLogs(models.Model):
    lojista_id = models.IntegerField(null=False)
    octadesk_id = models.CharField(max_length=50, null=True)
    method = models.CharField(max_length=10, null=False)
    payload = models.JSONField()
    status_code = models.IntegerField(null=False)
    message = models.Char<PERSON>ield(max_length=1000, null=False)
    sent_at = models.DateTimeField(null=False, auto_now_add=True)

    class Meta:
        managed = False
        db_table = '"octadesk\".\"logs"'
