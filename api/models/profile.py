from django.db import models
from django.contrib.auth.models import User
from django.dispatch import receiver
from django.db.models.signals import post_save
#from django.db.models import J<PERSON>NField
from api.models.cargo import DashboardCargo
from api.models.models import DashboardLojista



class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    distribuidor = models.TextField(max_length=500, blank=True)
    tipo_perfil = models.CharField(max_length=30, blank=True)
    tipo_projeto = models.CharField(max_length=30, null=True)
    tipo_cargo = models.ForeignKey(DashboardCargo, on_delete=models.CASCADE, related_name="tipo_cargo", null=True)
    projeto_id = models.IntegerField()

    class Meta:
        db_table = 'dashboard_profile'

class ProdutosAdquiridos(models.Model):
    cliente = models.ForeignKey(DashboardLojista, on_delete=models.DO_NOTHING, related_name="produtos_clientes", null=True)
    produtos_contratados = models.JSONField()
    documento = models.CharField(max_length=20)
    email = models.CharField(max_length=250)
    nome = models.CharField(max_length=250)
    tipo = models.CharField(max_length=10)
    telefone = models.CharField(max_length=30)

    class Meta:
        db_table = 'produtos_adquiridos'

# @receiver(post_save, sender=User)
# def create_user_profile(sender, instance, created, **kwargs):
#     if created:
#         Profile.objects.create(user=instance)

# @receiver(post_save, sender=User)
# def save_user_profile(sender, instance, **kwargs):
#     instance.profile.save()
