from django.db import models
from api.models.produto import Produto


class Cargo(models.Model):
    descricao = models.CharField(max_length=250, default='')
    data_cadastro = models.DateTimeField(null=True, auto_now_add=True)
    data_update = models.DateTimeField(null=True, auto_now=True)
    produto = models.ForeignKey(Produto, on_delete=models.CASCADE)

    class Meta:
        db_table = '"hierarquia\".\"api_cargo"'

    def __str__(self):
        return 'cargo'


class DashboardCargo(models.Model):
    descricao = models.CharField(max_length=50)
    data_cadastro = models.DateTimeField(null=True, auto_now_add=True)
    data_update = models.DateTimeField(null=True, auto_now=True)

    class Meta:
        db_table = 'dashboard_cargo'

    def __str__(self):
        return 'dashboard_cargo'
