# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey has `on_delete` set to the desired behavior.
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.contrib.auth.models import User
from django.db import models


# from django.contrib.postgres.fields import JSONField


class DashboardAtividadessecundaria(models.Model):
    atividade = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    cnpj = models.ForeignKey('DashboardCnpj', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_atividadessecundaria'


class DashboardCertidoesnegativasitens(models.Model):
    emitido_em = models.DateTimeField(blank=True, null=True)
    fonte = models.CharField(max_length=250, blank=True, null=True)
    protocolo = models.CharField(max_length=250, blank=True, null=True)
    url = models.CharField(max_length=250, blank=True, null=True)
    nada_consta = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_certidoesnegativasitens'


class DashboardCessao(models.Model):
    maquina_cessao = models.CharField(max_length=10)
    foto_documento = models.CharField(max_length=400)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)
    user = models.ForeignKey('AuthUser', models.DO_NOTHING)
    tipo_arquivo = models.CharField(max_length=100)
    tipo_produto = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'dashboard_cessao'


class DashboardCnpj(models.Model):
    cnpj = models.CharField(max_length=16, blank=True, null=True)
    nome_fantasia = models.CharField(max_length=150, blank=True, null=True)
    nome_empresarial = models.CharField(max_length=150, blank=True, null=True)
    data_abertura = models.DateField(blank=True, null=True)
    fonte = models.CharField(max_length=50, blank=True, null=True)
    atividade_principal = models.CharField(max_length=300, blank=True, null=True)
    faturamento_presumido = models.CharField(max_length=300, blank=True, null=True)
    numero_funcionarios = models.CharField(max_length=50, blank=True, null=True)
    numero_funcionarios_filiais = models.CharField(max_length=50)
    regime_tributario = models.CharField(max_length=16, blank=True, null=True)
    irs_status = models.CharField(max_length=16, blank=True, null=True)
    capital_social = models.CharField(max_length=16, blank=True, null=True)
    data_situacao_cadastral = models.DateField(blank=True, null=True)
    data_situacao_especial = models.DateField(blank=True, null=True)
    email = models.CharField(max_length=250, blank=True, null=True)
    ente_federativo_responsavel = models.CharField(max_length=300, blank=True, null=True)
    motivo_situacao_cadastral = models.CharField(max_length=250, blank=True, null=True)
    natureza_juridica = models.CharField(max_length=200, blank=True, null=True)
    numero = models.CharField(max_length=16, blank=True, null=True)
    situacao_cadastral = models.CharField(max_length=16, blank=True, null=True)
    situacao_especial = models.CharField(max_length=50, blank=True, null=True)
    telefone = models.CharField(max_length=150, blank=True, null=True)
    tipo = models.CharField(max_length=150, blank=True, null=True)
    localizacao_bairro = models.CharField(max_length=250, blank=True, null=True)
    localizacao_cep = models.CharField(max_length=10, blank=True, null=True)
    localizacao_cidade = models.CharField(max_length=50, blank=True, null=True)
    localizacao_complemento = models.CharField(max_length=150, blank=True, null=True)
    localizacao_estado = models.CharField(max_length=50, blank=True, null=True)
    localizacao_logradouro = models.CharField(max_length=150, blank=True, null=True)
    localizacao_numero = models.CharField(max_length=6, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING, related_name='cnpj')

    class Meta:
        managed = False
        db_table = 'dashboard_cnpj'


class DashboardCpf(models.Model):
    sexo = models.CharField(max_length=2)
    numero = models.CharField(max_length=25)
    data_de_nascimento = models.DateField()
    nome = models.CharField(max_length=300)
    renda = models.CharField(max_length=250)
    pep = models.BooleanField()
    situacao_imposto_de_renda = models.CharField(max_length=250)
    cpf_situacao_cadastral = models.CharField(max_length=250)
    cpf_data_de_inscricao = models.CharField(max_length=250)
    cpf_digito_verificador = models.CharField(max_length=4)
    cpf_anterior_1990 = models.CharField(max_length=1)
    ano_obito = models.CharField(max_length=1)
    grafia = models.CharField(max_length=300)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_cpf'


class DashboardDepartamentos(models.Model):
    codigo_departamento = models.IntegerField(blank=True, null=True)
    descricao_departamento = models.CharField(max_length=250, blank=True, null=True)
    id_mcc = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_departamentos'


class DashboardDistribuidores(models.Model):
    agencia = models.CharField(max_length=10, blank=True, null=True)
    conta = models.CharField(max_length=10)
    digito_conta = models.CharField(max_length=1)
    banco = models.IntegerField(blank=True, null=True)
    tipo_conta = models.CharField(max_length=250)
    cnpj = models.CharField(max_length=250)
    razao_social = models.CharField(max_length=250)

    class Meta:
        managed = False
        db_table = 'dashboard_distribuidores'


class DashboardDividaativa(models.Model):
    nome = models.CharField(max_length=150)
    valor_devido = models.CharField(max_length=20)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_dividaativa'


class TmpBancos(models.Model):
    cod = models.CharField(max_length=4, help_text="String [Código do banco do Lojista (Ex: 104)]")
    descricao = models.CharField(max_length=40, help_text="String [Nome do banco]")
    ispb = models.CharField(max_length=40, help_text="String [Código identificador dos bancos]")

    class Meta:
        managed = False
        db_table = 'tmp_bancos'


class DashboardDomiciliobancario(models.Model):
    banco = models.CharField(max_length=4, help_text="String [Código do banco do Lojista (Ex: 104)]")
    agencia = models.CharField(max_length=40, help_text="Inteiro [Agência bancária (Sem o dígito)]")
    conta = models.IntegerField(help_text="Inteiro [Conta bancária]")
    digito_conta = models.CharField(max_length=1, help_text="Inteiro [Dígito da conta]")
    tipo_conta = models.CharField(max_length=10, help_text="String [CORRENTE/POUPANÇA]")
    data_cadastro = models.DateTimeField(blank=True, null=True, help_text="Data de cadastro")
    data_update = models.DateTimeField(blank=True, null=True, help_text="Data de update")
    ativo = models.BooleanField(help_text="Indica se a conta é a ativa ou não")
    documento_titular = models.CharField(max_length=20, help_text="Documento do titular da conta")
    nome_titular = models.CharField(max_length=255, help_text="Nome do titular da conta")
    matriz_titular = models.CharField(max_length=32, help_text="Matriz IDWAll do cliente")
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING, related_name='domicilio_bancario',
                                help_text="Id do lojista cadastrado")

    class Meta:
        managed = False
        db_table = 'dashboard_domiciliobancario'


class DashboardDomiciliobancarioAux(models.Model):
    banco = models.CharField(max_length=4, help_text="String [Código do banco do Lojista (Ex: 104)]")
    agencia = models.CharField(max_length=40, help_text="Inteiro [Agência bancária (Sem o dígito)]")
    conta = models.IntegerField(help_text="Inteiro [Conta bancária]")
    digito_conta = models.CharField(max_length=1, help_text="Inteiro [Dígito da conta]")
    tipo_conta = models.CharField(max_length=10, help_text="String [CORRENTE/POUPANÇA]")
    data_cadastro = models.DateTimeField(blank=True, null=True, help_text="Data de cadastro")
    data_update = models.DateTimeField(blank=True, null=True, help_text="Data de update")
    ativo = models.BooleanField(help_text="Indica se a conta é a ativa ou não")
    documento_titular = models.CharField(max_length=20, help_text="Documento do titular da conta")
    nome_titular = models.CharField(max_length=255, help_text="Nome do titular da conta")
    matriz_titular = models.CharField(max_length=32, help_text="Matriz IDWAll do cliente")
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING, help_text="Id do lojista cadastrado")
    domicilio = models.ForeignKey('DashboardDomiciliobancario', models.DO_NOTHING, related_name='domicilio_bancario',
                                  help_text="Id do lojista cadastrado")
    status = models.BooleanField(help_text="Indica se a conta foi aprovada ou não", null=True)
    responsavel = models.CharField(max_length=255, help_text="Nome Responsavel pela Solicitação")

    class Meta:
        managed = False
        db_table = 'dashboard_domiciliobancario_aux'


class DashboardEmailslojista(models.Model):
    endereco = models.CharField(max_length=250)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_emailslojista'


class DashboardEmpresas(models.Model):
    cnpj = models.CharField(max_length=16)
    nome_empresarial = models.CharField(max_length=150)
    tipo_relacionamento = models.CharField(max_length=20)
    cargo = models.CharField(max_length=20)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_empresas'


class DashboardEnderecolojista(models.Model):
    principal = models.BooleanField(blank=True, null=True, help_text="Boolean [True/False] - Endereço principal?")
    cidade = models.CharField(max_length=50, help_text="String [Cidade do Lojista]")
    estado = models.CharField(max_length=2, help_text="String [Estado do Lojista]")
    numero = models.CharField(max_length=10, help_text="String [Número do endereco do lojista] Ex: 165a, 104")
    cep = models.CharField(max_length=9, help_text="String [Cep do endereço] Ex: 0000-000")
    complemento = models.CharField(max_length=250, help_text="String [Complemento do endereço do lojista]")
    logradouro = models.CharField(max_length=250, help_text="String [Logradouro do endereço do lojista]")
    bairro = models.CharField(max_length=250, help_text="String [Bairro do endereço do lojista]")
    tipo = models.CharField(max_length=250, help_text="String [Tipo do endereço do lojista] Ex: COMERCIAL/RESIDENCIAL")
    pessoas_endereco = models.CharField(max_length=3, help_text="String [Quantidade de pessoas no endereço]")
    utilizado_pedido = models.BooleanField(help_text="Boolean [Endereço utilizado na entrega?]")
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey('DashboardLojista', models.DO_NOTHING, related_name='endereco',
                                help_text="Inteiro [Id do lojista]")

    class Meta:
        managed = False
        db_table = 'dashboard_enderecolojista'


class DashboardGrafiascnpj(models.Model):
    grafia = models.CharField(max_length=250)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    cnpj = models.ForeignKey(DashboardCnpj, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_grafiascnpj'


class DashboardIdwall(models.Model):
    class Meta:
        managed = False
        db_table = 'dashboard_idwall'


class DashboardItemvalidacoes(models.Model):
    regra = models.CharField(max_length=250, blank=True, null=True)
    nome = models.CharField(max_length=250, blank=True, null=True)
    descricao = models.CharField(max_length=250, blank=True, null=True)
    resultado = models.CharField(max_length=250, blank=True, null=True)
    mensagem = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    validacao = models.ForeignKey('DashboardValidacao', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_itemvalidacoes'


class DashboardOperadoraGprs(models.Model):
    nome = models.CharField(max_length=250, default='')
    identificador = models.CharField(max_length=300, default='')
    provedor = models.CharField(max_length=250, default='')
    data_cadastro = models.DateTimeField(null=True)
    data_update = models.DateTimeField(null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_operadoragprs'


class DashboardBancos(models.Model):
    cod = models.CharField(max_length=20, help_text="String [Código bancário]")
    descricao = models.CharField(max_length=250, help_text="Descrição do banco")

    class Meta:
        managed = False
        db_table = 'dashboard_bancos'


class LimiteTransacional(models.Model):
    lojista_id = models.IntegerField()
    limite_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'dashboard_grupo_limite_tx'


class DashboardPlanos(models.Model):
    nome = models.CharField(max_length=250)
    tipo_contratacao = models.CharField(max_length=250)
    valor_contratacao = models.FloatField()
    taxa_debito = models.FloatField()
    taxa_credito = models.FloatField()
    taxa_parcelado = models.FloatField()
    tipo_projeto = models.CharField(max_length=40)
    final_vigencia = models.DateField()
    produto = models.ForeignKey('DashboardProdutos', models.DO_NOTHING)
    projeto = models.ForeignKey('DashboardProjetos', models.DO_NOTHING)
    id_parent = models.IntegerField()
    antecipacao = models.BooleanField(default=False)
    cargos = models.JSONField(null=True, blank=True)
    adquirencia = models.CharField(max_length=250)
    json_prazos = models.JSONField()
    json_taxasbandeiras = models.JSONField()
    p18 = models.BooleanField(default=False)
    split_ativo = models.BooleanField(default=False)
    split_favorecidos = models.JSONField()

    class Meta:
        managed = False
        db_table = 'dashboard_planos'


class DashboardLojista(models.Model):
    lojista_id = models.IntegerField(blank=True, null=True)
    nome = models.CharField(max_length=200, help_text="String [Nome do lojista]")
    documento = models.CharField(unique=True, max_length=20, help_text="String [Nº do documento do lojista]")
    branch_id = models.CharField(max_length=20, blank=True, null=True)
    merchant_id = models.CharField(max_length=20, blank=True, null=True)
    affiliation_key = models.CharField(max_length=250, blank=True, null=True)
    mensagem_idwall = models.CharField(max_length=250, blank=True, null=True)
    matriz_idwall = models.CharField(max_length=250,
                                     help_text='Campo referente ao tipo de cadastro que está sendo realizado: lucree_QSA_pf (Pessoa física) | lucree_QSA_pj (Pessoa jurídica)')
    numero_idwall = models.CharField(max_length=300, blank=True, null=True)
    resultado_idwall = models.CharField(max_length=250, blank=True, null=True)
    status_idwall = models.CharField(max_length=20, blank=True, null=True)
    pep_idwall = models.BooleanField(blank=True, null=True)
    fase = models.CharField(max_length=30)
    com_protesto = models.BooleanField(blank=True, null=True)
    operadora = models.ForeignKey(DashboardOperadoraGprs, models.DO_NOTHING, help_text="Id da operadora do chip")
    departamento = models.IntegerField(
        help_text="Inteiro [Código do departamento comercial (MCC)] Obs: consultar no método /departamentos")
    aprovacao_manual = models.BooleanField(blank=True, null=True)
    email_vendedor_responsavel = models.CharField(max_length=250, blank=False, null=False,
                                                  help_text="String [Email do vendedor]")
    relatorio_a_partir_pj = models.BooleanField(blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    agencia_favorecido = models.CharField(max_length=250, blank=True, null=True)
    banco_favorecido = models.IntegerField(blank=True, null=True)
    conta_favorecido = models.CharField(max_length=250, blank=True, null=True)
    digito_conta_favorecido = models.CharField(max_length=250, blank=True, null=True)
    porcentagem_favorecido = models.CharField(max_length=250, blank=True, null=True)
    tipo_conta_favorecido = models.CharField(max_length=250, blank=True, null=True)
    mensagem_integrador = models.CharField(max_length=250, blank=True, null=True)
    num_terminais = models.IntegerField(help_text="String [Nº de terminais]")
    observacao_liberacao_risco = models.CharField(max_length=1040, blank=True, null=True)
    porcentagem_cliente = models.CharField(max_length=250, blank=True, null=True)
    cnpj_favorecido = models.CharField(max_length=250, blank=True, null=True)
    fase_integracao = models.CharField(max_length=30, default="", null=True)
    nome_razao_favorecido = models.CharField(max_length=250, blank=True, null=True)
    plano = models.ForeignKey(DashboardPlanos, models.DO_NOTHING, help_text='Plano retornado no endpoint /planos',
                              related_name='plano_fisico', null=True)
    plano_virtual = models.ForeignKey(DashboardPlanos, models.DO_NOTHING,
                                      help_text='Plano virtual retornado no endpoint /planos',
                                      related_name='plano_virtual', null=True)
    distribuidor = models.ForeignKey(DashboardDistribuidores, models.DO_NOTHING, blank=True, null=True)
    origem = models.CharField(max_length=250, default='', blank=True, null=True)
    produto = models.CharField(max_length=100, default='', blank=True, null=True,
                               help_text='Lucree Auto | CredMoura | Quero Delivery')
    cadastro_ativo = models.BooleanField(blank=True, null=True, default=True)
    flag_link_pagto = models.IntegerField(null=True, default=0)
    codigo_cliente = models.CharField(max_length=255, blank=True, null=True)
    flag_pagto_18x = models.IntegerField(null=True, default=0)
    plano_18x = models.ForeignKey(DashboardPlanos, models.DO_NOTHING,
                                  help_text='Plano 18x retornado no endpoint planos', related_name='plano_fisico_18x',
                                  null=True)
    multi_agenda = models.BooleanField(default=False)
    rdstation_id = models.CharField(max_length=2000, null=True)
    octadesk_id = models.CharField(max_length=2000, null=True)
    cerc_id = models.CharField(max_length=250, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_lojista'


class DashboardLojistaterminal(models.Model):
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista_id = models.ForeignKey(DashboardLojista, models.DO_NOTHING)
    terminal_id = models.ForeignKey('DashboardTerminais', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_lojistaterminal'


class DashboardNomefonteconsulta(models.Model):
    nome = models.CharField(max_length=150)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    resumo_consulta = models.ForeignKey('DashboardResumoconsultas', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_nomefonteconsulta'


class DashboardPagamento(models.Model):
    id = models.AutoField(primary_key=True)
    nsu = models.CharField(max_length=250, blank=True, null=True,
                           help_text="String [Código do comprovante de pagamento]")
    data_pagamento = models.CharField(max_length=45, blank=True, null=True, help_text="Date [Data do pagamento]")
    pagamento_valido = models.BooleanField(blank=True, null=True, default=False,
                                           help_text="Boolean [True/False] - Pagamento válido?")
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    cod_autorizacao = models.CharField(max_length=10, blank=True, null=True,
                                       help_text="String [Código de autorização do pagamento]")
    valor = models.CharField(max_length=10, blank=True, null=True,
                             help_text="String [Valor da transação]")
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING, help_text="Id do lojista cadastrado")

    class Meta:
        managed = False
        db_table = 'dashboard_pagamento'


class DadosSocietariosEc(models.Model):
    id = models.AutoField(primary_key=True)
    nome = models.CharField(max_length=250, blank=True, null=True, help_text="Nome")
    documento = models.CharField(max_length=40, blank=True, null=True, help_text="documento")
    documento_tipo = models.CharField(max_length=10, blank=True, null=True, help_text="documento_tipo")
    nivel_societario_secundario = models.JSONField()
    created_at = models.DateTimeField(blank=True, null=True, auto_now=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING, help_text="Id do lojista cadastrado")

    class Meta:
        managed = False
        db_table = 'dados_societarios_ec'


class AuthUser(models.Model):
    password = models.CharField(max_length=128)
    last_login = models.DateTimeField(blank=True, null=True)
    is_superuser = models.BooleanField()
    username = models.CharField(unique=True, max_length=150)
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=150)
    email = models.CharField(max_length=254)
    is_staff = models.BooleanField()
    is_active = models.BooleanField()
    date_joined = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'auth_user'


class DashboardPedido(models.Model):
    numero_pedido = models.IntegerField(help_text="Inteiro [Nº do pedido]")
    status_pedido = models.CharField(max_length=250, help_text="String [Status do pedido] Ex: Criado via e-commerce")
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    comercial = models.ForeignKey(AuthUser, models.DO_NOTHING, blank=True, null=True,
                                  help_text="Inteiro [Usuario responsável] Obs; A priori será criado um usuário para o E-commerce")
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING, help_text="Inteiro [Lojista cadastrado]")
    pagamento = models.ForeignKey(DashboardPagamento, models.DO_NOTHING, blank=True, null=True,
                                  help_text="Inteiro [Pagamento cadastrado]")

    class Meta:
        managed = False
        db_table = 'dashboard_pedido'


class DashboardParticipacaoempresasitens(models.Model):
    cnpj = models.CharField(max_length=20, blank=True, null=True)
    nome_empresarial = models.CharField(max_length=150, blank=True, null=True)
    tipo_relacionamento = models.CharField(max_length=200, blank=True, null=True)
    cargo = models.CharField(max_length=150, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_participacaoempresasitens'


class DashboardPaystore(models.Model):
    class Meta:
        managed = False
        db_table = 'dashboard_paystore'


# class DashboardPedido(models.Model):
#     numero_pedido = models.IntegerField()
#     status_pedido = models.CharField(max_length=250)
#     data_cadastro = models.DateTimeField(blank=True, null=True)
#     data_update = models.DateTimeField(blank=True, null=True)
#     comercial = models.ForeignKey(AuthUser, models.DO_NOTHING, blank=True, null=True)
#     lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)
#     pagamento = models.ForeignKey(DashboardPagamento, models.DO_NOTHING, blank=True, null=True)
#
#     class Meta:
#         managed = False
#         db_table = 'dashboard_pedido'


class DashboardPessoasrelacionadas(models.Model):
    cpf = models.CharField(max_length=25)
    nome = models.CharField(max_length=300)
    tipo = models.CharField(max_length=20)
    cargo = models.CharField(max_length=20)
    cnpj = models.CharField(max_length=16)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_pessoasrelacionadas'


class DashboardProjetos(models.Model):
    nome = models.CharField(blank=False, null=False, max_length=100)
    url = models.CharField(blank=False, null=False, max_length=250)
    no_pai = models.IntegerField(blank=False, null=False)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_projetos'


class DashboardProdutos(models.Model):
    descricao = models.CharField(max_length=250)
    identificador = models.CharField(max_length=250)
    gprs = models.BooleanField(blank=True, null=True)
    wifi = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_produtos'


class DashboardProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    distribuidor = models.TextField(max_length=500, blank=True)
    tipo_perfil = models.CharField(max_length=30, blank=True)

    class Meta:
        managed = False
        db_table = 'dashboard_profile'


class DashboardProtesto(models.Model):
    estado_protesto = models.CharField(max_length=2)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_protesto'


class DashboardQsacnpj(models.Model):
    nome = models.CharField(max_length=250)
    nome_representante = models.CharField(max_length=250, blank=True, null=True)
    pais_origem = models.CharField(max_length=250, blank=True, null=True)
    qualificacao = models.CharField(max_length=250)
    qualificacao_representante_legal = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    cnpj = models.ForeignKey(DashboardCnpj, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_qsacnpj'


class DashboardRegistration(models.Model):
    class Meta:
        managed = False
        db_table = 'dashboard_registration'


class DashboardResponsavel(models.Model):
    nome = models.CharField(max_length=250, blank=True, null=True, help_text='Nome do responsável ou sócio')
    documento = models.CharField(max_length=20, blank=True, null=True, help_text='CPF do responsável')
    tipo = models.CharField(max_length=40, blank=True, null=True,
                            help_text='Tipo do responsável (10 - Contato/9 - Sócio)')
    data_nascimento = models.CharField(max_length=40, blank=True, null=True,
                                       help_text='Data de nascimento ex: DD/MM/AAAA')
    sexo = models.CharField(max_length=1, blank=True, null=True, help_text='Sexo: M ou F')
    ddd = models.CharField(max_length=2, blank=True, null=True, help_text='DDD')
    telefone = models.CharField(max_length=20, blank=True, null=True, help_text='Telefone')
    email = models.CharField(max_length=250, blank=True, null=True, help_text='Email')
    nacionalidade = models.CharField(max_length=20, blank=True, null=True, help_text='Brasileira ou Estrangeira')
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)
    ocupacao = models.CharField(max_length=250, null=True, blank=True, help_text='Ocupação')
    profissao = models.CharField(max_length=250, null=True, blank=True, help_text='Profissão')

    class Meta:
        managed = False
        db_table = 'dashboard_responsavel'


class DashboardResumoconsultas(models.Model):
    nome_matriz = models.CharField(max_length=150)
    status_protocolo = models.CharField(max_length=20)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_resumoconsultas'


class DashboardAuditoriaPlanos(models.Model):
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)
    plano = models.ForeignKey(DashboardPlanos, models.DO_NOTHING, related_name='plano', null=True)
    data_inicio_utilizacao = models.DateTimeField(blank=True, null=True, auto_now=True)
    data_fim_utilizacao = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'auditoria_planos'


class DashboardRetorno4Ward(models.Model):
    id_type = models.IntegerField()
    id_number = models.CharField(max_length=20)
    codigo_adquirente = models.IntegerField()
    status = models.IntegerField()
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista_id = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_retorno4ward'


class DashboardSintegraitem(models.Model):
    cnpj = models.CharField(max_length=20, blank=True, null=True)
    inscricao_estadual = models.CharField(max_length=20, blank=True, null=True)
    razao_social = models.CharField(max_length=250, blank=True, null=True)
    cnae_principal = models.CharField(max_length=250, blank=True, null=True)
    situacao_cadastral_vigente = models.CharField(max_length=20, blank=True, null=True)
    data_situacao_cadastral = models.DateField(blank=True, null=True)
    regime_apuracao = models.CharField(max_length=250, blank=True, null=True)
    data_credenciamento_nfe = models.CharField(max_length=250, blank=True, null=True)
    indicador_obrigatoriedade_nfe = models.CharField(max_length=250, blank=True, null=True)
    data_obrigatoriedade_nfe = models.CharField(max_length=250, blank=True, null=True)
    protocolo = models.CharField(max_length=250, blank=True, null=True)
    tipo = models.CharField(max_length=250, blank=True, null=True)
    uf = models.CharField(max_length=2)
    cad_icms = models.CharField(max_length=250, blank=True, null=True)
    cae = models.CharField(max_length=250, blank=True, null=True)
    nome_fantasia = models.CharField(max_length=250, blank=True, null=True)
    data_inicio_atividade = models.CharField(max_length=250, blank=True, null=True)
    natureza_juridica = models.CharField(max_length=250, blank=True, null=True)
    natureza_estabelecimento = models.CharField(max_length=250, blank=True, null=True)
    data_abertura = models.CharField(max_length=250, blank=True, null=True)
    data_baixa = models.CharField(max_length=250, blank=True, null=True)
    delegacia_fazendaria = models.CharField(max_length=250, blank=True, null=True)
    enquadramento_empresa = models.CharField(max_length=250, blank=True, null=True)
    observacao = models.CharField(max_length=250, blank=True, null=True)
    data_inscricao = models.CharField(max_length=250, blank=True, null=True)
    ped = models.CharField(max_length=250, blank=True, null=True)
    data_inicio_ped = models.CharField(max_length=250, blank=True, null=True)
    ultrapassou_limite_estadual = models.CharField(max_length=250, blank=True, null=True)
    data_inicio_simples_nacional = models.CharField(max_length=250, blank=True, null=True)
    condicao = models.CharField(max_length=250, blank=True, null=True)
    email = models.CharField(max_length=250, blank=True, null=True)
    indicador_obrigatoriedade_efd = models.CharField(max_length=250, blank=True, null=True)
    data_obrigatoriedade_efd = models.CharField(max_length=250, blank=True, null=True)
    opcao_simples = models.CharField(max_length=250, blank=True, null=True)
    indicador_obrigatoriedade_cte = models.CharField(max_length=250, blank=True, null=True)
    data_obrigatoriedade_cte = models.CharField(max_length=250, blank=True, null=True)
    situacao_sintegra = models.CharField(max_length=250, blank=True, null=True)
    tipo_unidade_auxiliar = models.CharField(max_length=250, blank=True, null=True)
    regime_pagamento = models.CharField(max_length=250, blank=True, null=True)
    situacao_contribuinte = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_sintegraitem'


class DashboardSintegraitematividadesecundaria(models.Model):
    codigo = models.CharField(max_length=50, blank=True, null=True)
    descricao = models.CharField(max_length=150, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    sintegra_item = models.ForeignKey(DashboardSintegraitem, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_sintegraitematividadesecundaria'


class DashboardSkipper(models.Model):
    class Meta:
        managed = False
        db_table = 'dashboard_skipper'


class DashboardSlack(models.Model):
    class Meta:
        managed = False
        db_table = 'dashboard_slack'


class DashboardTaxas(models.Model):
    credito_a_vista = models.FloatField()
    credito_parcelado_emissor = models.FloatField()
    debito = models.FloatField()
    parcelado_loja_dois_seis = models.FloatField()
    parcelado_loja_sete_doze = models.FloatField()
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_taxas'


class DashboardTelefones(models.Model):
    fone_ddd = models.CharField(max_length=2, blank=True, null=True)
    fone_numero = models.CharField(max_length=10, blank=True, null=True)
    principal = models.BooleanField()
    tipo_assinante = models.CharField(max_length=20, blank=True, null=True)
    pessoas_com_o_mesmo_telefone = models.CharField(max_length=5, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_telefones'


class DashboardTentativasresumoconsulta(models.Model):
    duracao_tentativa = models.FloatField()
    hora_fim_tentativa = models.DateTimeField(blank=True, null=True)
    hora_inicio_tentativa = models.DateTimeField(blank=True, null=True)
    msg_erro_tentativa = models.CharField(max_length=250)
    status_fonte = models.CharField(max_length=10)
    status_tentativa = models.CharField(max_length=25)
    tipo_erro_tentativa = models.CharField(max_length=250, blank=True, null=True)
    fonte = models.ForeignKey(DashboardNomefonteconsulta, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_tentativasresumoconsulta'


class DashboardTerminais(models.Model):
    terminal_id_logical = models.CharField(max_length=50)
    terminal_id_physical = models.CharField(max_length=50)
    tecnology_code = models.IntegerField()
    terminal_serial_number = models.CharField(max_length=50)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_terminais'


class DashboardTransacoes(models.Model):
    nsu = models.IntegerField(blank=True, null=True)
    acquirer_nsu = models.CharField(max_length=40, blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    parcels = models.IntegerField(blank=True, null=True)
    brand = models.CharField(max_length=40, blank=True, null=True)
    start_date = models.DateTimeField(blank=True, null=True)
    finish_date = models.DateTimeField(blank=True, null=True)
    confirmation_date = models.DateTimeField(blank=True, null=True)
    payment_date = models.DateTimeField(blank=True, null=True)
    response_code = models.CharField(max_length=10, blank=True, null=True)
    response_message = models.CharField(max_length=250, blank=True, null=True)
    authorization_number = models.CharField(max_length=40, blank=True, null=True)
    terminal = models.CharField(max_length=40, blank=True, null=True)
    tef_terminal = models.CharField(max_length=40, blank=True, null=True)
    terminal_serial_number = models.CharField(max_length=40, blank=True, null=True)
    terminal_manufacturer = models.CharField(max_length=40, blank=True, null=True)
    terminal_model = models.CharField(max_length=40, blank=True, null=True)
    terminal_type = models.CharField(max_length=40, blank=True, null=True)
    acquirer = models.CharField(max_length=40, blank=True, null=True)
    merchant = models.CharField(max_length=40, blank=True, null=True)
    tef_merchant = models.CharField(max_length=40, blank=True, null=True)
    merchant_category_code = models.CharField(max_length=40, blank=True, null=True)
    merchant_national_type = models.CharField(max_length=40, blank=True, null=True)
    merchant_national_id = models.CharField(max_length=40, blank=True, null=True)
    product_name = models.CharField(max_length=40, blank=True, null=True)
    product_id = models.CharField(max_length=40, blank=True, null=True)
    card_input_method = models.CharField(max_length=40, blank=True, null=True)
    requested_password = models.CharField(max_length=40, blank=True, null=True)
    fallback = models.CharField(max_length=40, blank=True, null=True)
    origin = models.CharField(max_length=40, blank=True, null=True)
    authorization_time = models.IntegerField(blank=True, null=True)
    client_version = models.CharField(max_length=250, blank=True, null=True)
    server_version = models.CharField(max_length=250, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dashboard_transacoes'


class DashboardValidacao(models.Model):
    mensagem = models.CharField(max_length=250, blank=True, null=True)
    nome = models.CharField(max_length=250, blank=True, null=True)
    resultado = models.CharField(max_length=250, blank=True, null=True)
    status = models.CharField(max_length=250, blank=True, null=True)
    data_cadastro = models.DateTimeField(blank=True, null=True)
    data_update = models.DateTimeField(blank=True, null=True)
    lojista = models.ForeignKey(DashboardLojista, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'dashboard_validacao'


# class DjangoAdminLog(models.Model):
#     action_time = models.DateTimeField()
#     object_id = models.TextField(blank=True, null=True)
#     object_repr = models.CharField(max_length=200)
#     action_flag = models.SmallIntegerField()
#     change_message = models.TextField()
#     content_type = models.ForeignKey('DjangoContentType', models.DO_NOTHING, blank=True, null=True)
#     user = models.ForeignKey(AuthUser, models.DO_NOTHING)
#
#     class Meta:
#         managed = False
#         db_table = 'django_admin_log'


class DjangoContentType(models.Model):
    app_label = models.CharField(max_length=100)
    model = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'django_content_type'
        unique_together = (('app_label', 'model'),)


class DjangoMigrations(models.Model):
    app = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    applied = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_migrations'


class DjangoSession(models.Model):
    session_key = models.CharField(primary_key=True, max_length=40)
    session_data = models.TextField()
    expire_date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'django_session'


class DumpCredenciamento(models.Model):
    field_field = models.TextField(db_column='\ufeff--', blank=True,
                                   null=True)  # Field renamed to remove unsuitable characters. Field renamed because it started with '_'. Field renamed because it ended with '_'.

    class Meta:
        managed = False
        db_table = 'dump_credenciamento'


class LojistaAtivacaoterminal(models.Model):
    communication_profile = models.CharField(max_length=200)
    merchant = models.CharField(max_length=10)
    terminal = models.CharField(max_length=15)

    class Meta:
        managed = False
        db_table = 'lojista_ativacaoterminal'


class LojistaPaystore(models.Model):
    class Meta:
        managed = False
        db_table = 'lojista_paystore'


class Tparcelas(models.Model):
    nroparcela = models.IntegerField(blank=True, null=True)
    nroparcela_final = models.IntegerField(blank=True, null=True)
    desc_referencia = models.TextField(blank=True, null=True)
    nro_incremento = models.IntegerField(blank=True, null=True)
    idmodalidade = models.CharField(max_length=40, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tparcelas'


class Ttmp(models.Model):
    data = models.DateField(primary_key=True)
    nr_dia = models.IntegerField(blank=True, null=True)
    tt_dia_mes = models.IntegerField(blank=True, null=True)
    nr_mes = models.IntegerField(blank=True, null=True)
    nr_ano = models.IntegerField(blank=True, null=True)
    dt_dia_util_anterior = models.DateField(blank=True, null=True)
    dt_proximo_dia_util = models.DateField(blank=True, null=True)
    fl_dia_util = models.IntegerField(blank=True, null=True)
    fl_dia_util_incluindo_sabado = models.IntegerField(blank=True, null=True)
    fl_feriado = models.IntegerField(blank=True, null=True)
    nr_dia_semana = models.IntegerField(blank=True, null=True)
    ds_dia_semana = models.TextField(blank=True, null=True)
    nr_semana = models.IntegerField(blank=True, null=True)
    nr_semana_mes = models.IntegerField(blank=True, null=True)
    nr_dia_ano = models.IntegerField(blank=True, null=True)
    qt_dias_uteis_mes = models.IntegerField(blank=True, null=True)
    tt_dias_uteis_mes = models.IntegerField(blank=True, null=True)
    qt_dias_uteis_ano = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'ttmp'


# LUC-753
class UsuarioVenda(models.Model):
    user = models.ForeignKey(AuthUser, on_delete=models.CASCADE)
    lojista = models.ForeignKey(DashboardLojista, on_delete=models.CASCADE)

    class Meta:
        db_table = "dashboard_usuariovenda"


class TcnaeMcc(models.Model):
    id_mcc = models.IntegerField()
    desc_atividade_principal = models.TextField()

    class Meta:
        db_table = "tcnae_mcc"


class SistemaGeradorCredito(models.Model):
    documento = models.CharField(max_length=20)
    documento_gestor_credito = models.CharField(max_length=20)
    projeto = models.ForeignKey(DashboardProjetos, on_delete=models.CASCADE)

    class Meta:
        db_table = "sistema_gerador_credito"


class AuditoriaAlteracaoDomicilioBancario(models.Model):
    responsavel = models.CharField(max_length=500, null=False)
    acao = models.CharField(max_length=25, null=False)
    local_alteracao = models.CharField(max_length=25, null=False)
    banco = models.CharField(max_length=4, null=False)
    conta = models.CharField(max_length=30, null=False)
    digito_conta = models.CharField(max_length=1, null=False)
    agencia = models.CharField(max_length=30, null=False)
    tipo_conta = models.CharField(max_length=30, null=False)
    nome_titular = models.CharField(max_length=500, null=False)
    documento_titular = models.CharField(max_length=30, null=False)
    resultado_alteracao = models.TextField(null=False)
    data_alteracao = models.DateTimeField(auto_now_add=True)
    cliente = models.ForeignKey(DashboardLojista, on_delete=models.DO_NOTHING)

    class Meta:
        db_table = "auditoria_alteracao_domicilio_bancario"


class AllowContaTerceiros(models.Model):
    documento = models.TextField(null=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "allow_conta_terceiros"


class AuditoriaContaTerceiros(models.Model):
    responsavel = models.TextField(null=False)
    tipo_alteracao = models.TextField(null=False)
    local_alteracao = models.TextField(null=False)
    data_alteracao = models.DateTimeField(auto_now_add=True)
    cliente = models.ForeignKey(DashboardLojista, on_delete=models.DO_NOTHING)

    class Meta:
        db_table = "auditoria_conta_terceiros"


class DashboardSublojista(models.Model):
    lojista_id = models.IntegerField(null=False)
    email = models.TextField(unique=True)

    class Meta:
        db_table = 'dashboard_sublojista'


class OnboardingV2(models.Model):
    documento = models.CharField(max_length=20, primary_key=True)
    dados = models.JSONField()
    estagios = models.JSONField()
    responsavel = models.CharField(blank=True, null=True)
    responsavel_ultima_modificacao = models.CharField(blank=True, null=True)
    observacao = models.CharField(blank=True, null=True)
    finalizado = models.BooleanField(default=False)
    data_criacao = models.DateTimeField(auto_now_add=True)
    data_atualizacao = models.DateTimeField(auto_now=True)
    status_geral = models.CharField(max_length=20, default='andamento')

    class Meta:
        managed = False
        db_table = 'onboarding_v2.cadastro'

    def __str__(self):
        return self.documento
