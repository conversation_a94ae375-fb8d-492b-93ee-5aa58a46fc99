from django.db import models


class Auditoria(models.Model):
    criador = models.CharField(max_length=100, null=False)
    formulario = models.JSONField()
    status = models.BooleanField(default=False, null=False)
    observacao = models.CharField(max_length=200, null=False)
    data_criacao = models.DateTimeField(null=True, auto_now_add=True)
    data_update = models.DateTimeField(null=True)

    class Meta:
        db_table = "auditoria_cadastro"
