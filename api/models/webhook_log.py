from django.db import models

from api.models.webhooks import WebhooksCredenciamento

class WebhookLog(models.Model):
    id_webhook = models.SmallIntegerField()
    request_payload = models.TextField()
    response_body = models.TextField()
    response_status = models.CharField(max_length=255)
    data_criacao = models.DateTimeField(null=True, auto_now_add=True)

    class Meta:
        db_table = 'webhook_log'

    def __str__(self):
        return 'webhook_log'
