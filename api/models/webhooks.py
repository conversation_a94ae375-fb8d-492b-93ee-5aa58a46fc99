from django.db import models


class WebhooksCredenciamento(models.Model):
    descricao = models.CharField(max_length=255)
    endpoint = models.TextField()
    has_authentication = models.BooleanField()
    username = models.TextField()
    password = models.TextField()
    url_auth = models.TextField()

    class Meta:
        db_table = 'webhooks_credenciamento'

    def __str__(self):
        return 'webhooks_credenciamento'
