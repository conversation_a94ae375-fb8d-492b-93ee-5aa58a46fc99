from django.db import models


class Produto(models.Model):
    nome = models.CharField(max_length=250, default='')
    data_cadastro = models.DateTimeField(null=True, auto_now_add=True)
    data_update = models.DateTimeField(null=True, auto_now=True)

    class Meta:
        db_table = '"hierarquia\".\"api_produto"'

    def __str__(self):
        return 'cargo'

class GpProduto(models.Model):
    produto = models.CharField(max_length=150, null=False)
    gpproduto = models.CharField(max_length=150)
    bu = models.CharField(max_length=30, null=True)

    class Meta:
        managed = False
        db_table = "credenciamento\".\"gpproduto"