from django.db import models
from .cargo import Cargo
from .produto import Produto


class Hierarquia(models.Model):
    id_usu = models.IntegerField(default=0, null=False)
    usu_nome = models.CharField(max_length=250, default='', null=False)
    usu_parent = models.IntegerField(default=0, blank=True, null=True)
    cargo = models.ForeignKey(Cargo, on_delete=models.CASCADE, related_name='cargo_hierarquico')
    data_cadastro = models.DateTimeField(null=True, auto_now_add=True)
    data_update = models.DateTimeField(null=True, auto_now=True)
    produto = models.ForeignKey(Produto, null=True, on_delete=models.CASCADE, related_name='produto_hierarquia')
    documento_cpf = models.CharField(max_length=14, default='', null=False)
    documento_cnpj = models.CharField(max_length=14, default='', null=False)
    razao_social = models.Char<PERSON>ield(max_length=14, default='', null=False)

    class Meta:
        db_table = '"hierarquia\".\"api_hierarquia"'

    def __str__(self):
        return 'hierarquia'

