#!/bin/bash

setup () {
    echo  ------- SETUP -------
    apt-get update
    apt-get install -y zip
    python -m venv .venv
    source .venv/bin/activate
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    return $?
}

deploy() {
    echo ------- DEPLOY -------
    echo $1
    zappa update $1 || zappa deploy $1
    zappa certify $1 --yes
    return $?
}

setup && deploy $1
