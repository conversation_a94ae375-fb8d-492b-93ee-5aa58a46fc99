from django.urls import path, include
from drf_spectacular.views import SpectacularAPIView
from rest_framework.schemas import get_schema_view
from django.http import HttpResponse
from api.views import swagger
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static

def index(request):
    return HttpResponse("API OK!")


DESCRICAO = '''

# Ol<PERSON>, esta é a documentação da api de Credenciamento da **Lucree**

'''
schema_view = get_schema_view(title='Credenciamento Lucree', description=DESCRICAO)

urlpatterns = [
    path('admin/', admin.site.urls),
    path("", swagger.SpectacularRapiDocView.as_view(), name='docs'),
    path('schema', SpectacularAPIView.as_view(), name='schema'),
    path('', include('api.urls')),
    path('health-check', index)
]+ static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
