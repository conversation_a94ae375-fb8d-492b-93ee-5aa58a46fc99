import logging
import time


class Logger:
    def __init__(self):
        self.ogigem = "LUCREE API CREDENCIAMENTO"
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)

        self.LOG_LEVELS = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
        }

    def registrar(self, message, level="ERROR"):
        if level not in self.LOG_LEVELS:
            level = "ERROR"
            message = f"O nível de log informado não é válido: {message}"
            self.logger.error(message)
        else:
            self.logger.log(
                self.LOG_LEVELS[level],
                "[%s] - [%s] %s: %s",
                self.ogigem,
                self._get_formatted_time(),
                level,
                message,
            )
        return message

    def _get_formatted_time(self):
        return time.strftime("%d/%m/%Y %H:%M:%S")
