import os
from pathlib import Path

from decouple import config

# Static Config
API_DOCUMENTOS_DOWNLOAD = config('API_DOCUMENTOS_DOWNLOAD')
API_AUTH = config('API_AUTH')
CLIENTE_ID = config('CLIENTE_ID')
CLIENTE_SECRET = config('CLIENTE_SECRET')

S3_BUCKET = config('S3_BUCKET')
AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME')
AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY')


STATICFILES_STORAGE = "django_s3_storage.storage.StaticS3Storage"
AWS_STORAGE_BUCKET_NAME = S3_BUCKET
AWS_S3_BUCKET_NAME_STATIC = S3_BUCKET
AWS_S3_CUSTOM_DOMAIN = '%s.s3.us-west-2.amazonaws.com' % S3_BUCKET
STATIC_URL = "https://%s/" % AWS_S3_CUSTOM_DOMAIN
AWS_DEFAULT_ACL = None

SLACK_WEBHOOK_RISC_ENDPOIT = config('SLACK_WEBHOOK_RISC_ENDPOIT')
#
RDS_DB_NAME = config('RDS_DB_NAME')
RDS_USERNAME = config('RDS_DB_NAME')
RDS_PASSWORD = config('RDS_DB_NAME')
RDS_HOSTNAME = config('RDS_HOSTNAME')
# #
IDWALL_ENDPOINT = config('IDWALL_ENDPOINT')
IDWALL_TOKEN = config('IDWALL_TOKEN')

RDSTATION_CLIENT_ID = config('RDSTATION_CLIENT_ID')
RDSTATION_CLIENT_SECRET = config('RDSTATION_CLIENT_SECRET')
RDSTATION_BASE_PATH = config('RDSTATION_BASE_PATH')

OCTADESK_AUTH_URL=config('OCTADESK_AUTH_URL')
OCTADESK_API_TOKEN=config('OCTADESK_API_TOKEN')
OCTADESK_PERSON_BASE_URL=config('OCTADESK_PERSON_BASE_URL')

# Build paths inside the project like this= os.path.join(BASE_DIR, ...)
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', 'asd')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'api',
    'rest_framework',
    'rest_framework_swagger',
    'django_s3_storage',
    'corsheaders',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

CORS_ORIGIN_ALLOW_ALL = True

ROOT_URLCONF = 'credenciamento.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': ['templates'],
        'APP_DIRS': BASE_DIR / 'templates',
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'credenciamento.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': config('RDS_DB_NAME'),
        'USER': config('RDS_USERNAME'),
        'PASSWORD': config('RDS_PASSWORD'),
        'HOST': config('RDS_HOSTNAME'),
        'PORT': 5432,
        'OPTIONS': {
            'options': '-c search_path=credenciamento'
        }
    }
}

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True

LOG_LEVEL = config('LOG_LEVEL', 'INFO')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[%(levelname)s] - [%(asctime)s] - %(module)s - %(message)s'
        }
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        }
    },
    'handlers': {
        'console': {
            'level': LOG_LEVEL,
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            },
        },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        '': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False
        },
    },
}


# REST_FRAMEWORK = {
#     'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
#     'PAGE_SIZE': 50
# }
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'
REST_FRAMEWORK = {
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 50,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer'
    ]
}
SPECTACULAR_SETTINGS = {
    'TITLE': 'Lucree API Credenciaento',
    'DESCRIPTION': 'Api de Credenciamento',
    'VERSION': '1.6.2',
    'SERVE_INCLUDE_SCHEMA': False,
    'SERVE_PUBLIC': True,

    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'defaultModelRendering': 'model',
        'defaultModelsExpandDepth': 2,
        'defaultModelExpandDepth': 2,
        'docExpansion': 'none',
        'persistAuthorization': True,
        'displayOperationId': True,
        'tryItOutEnabled': True,
        'filter': True,
    },
    'SWAGGER_UI_DIST': 'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/3.52.5',
    'SWAGGER_UI_FAVICON_HREF': 'https://www.seusite.com.br/path/to/favicon.ico',
    'LOGO_URL': 'http://127.0.0.1:8000/static/imagens/branco.png',
    'SWAGGER_UI_THEME': 'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/3.52.5/swagger-ui.css',
}
