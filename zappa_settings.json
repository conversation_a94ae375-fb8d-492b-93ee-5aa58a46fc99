{"hml": {"aws_region": "us-west-2", "django_settings": "credenciamento.settings", "project_name": "credenciamento", "runtime": "python3.11", "s3_bucket": "zappa-lambda-bucket", "apigateway_description": "API de Credenciamento Lucree - HML", "lambda_description": "API de Credenciamento Lucree - HML", "memory_size": 512, "log_level": "DEBUG", "cors": true, "keep_warm": false, "domain": "api-hml.lucree.com.br", "base_path": "credenciamento", "endpoint_configuration": ["REGIONAL"], "route53_enabled": false, "certificate_arn": "arn:aws:acm:us-east-1:175572419266:certificate/05725bab-8c5b-406f-a18d-cd26e4c9cfe8", "environment_variables": {"API_DOCUMENTOS_DOWNLOAD": "https://api-hml.lucree.com.br/documentos/download/", "API_AUTH": "https://api.lucree.com.br/autenticacao/v1", "CLIENTE_ID": "3MVG9_I_oWkIqLrm9iSgCRoKVeNZfZWqeqVvXj6xLO09oiqANou2dwSgyZd8UYN6t9xwxKgcE7GoRHfGIZf0G", "CLIENTE_SECRET": "EE4C400EDA5C5B52EF31D172C2512204072AD65C8E0BA9C75C2C191DEFBE9B5F", "RDS_DB_NAME": "subadiqdb1", "RDS_USERNAME": "subadiqdb", "RDS_PASSWORD": "}6H:6;iGPd]", "RDS_HOSTNAME": "subadiq-homolog.cxmg4sq9azs9.us-west-2.rds.amazonaws.com", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "CIaIYfcT70PzYIS4LCTRCnJwHT7+Bxg6LheI4+Oe", "S3_BUCKET": "swagger-static", "AWS_S3_REGION_NAME": "us-west-2", "SECRET_KEY": "sxs4ti#4^bn2md#!+)v-l9q+qv=6l2-5$79=t0j8-_#-^n+#$(", "IDWALL_ENDPOINT": "https://api-v2.idwall.co/relatorios", "IDWALL_TOKEN": "9958bcb5-9320-4dc2-b443-4e1745ca86e5", "LOG_LEVEL": "DEBUG", "SLACK_WEBHOOK_RISC_ENDPOIT": "*****************************************************************************", "API_SANKHYA_KEY": "EHgIvCVOov697B1hc4RHc83rt84PJwUh2PzOTRLb", "API_SANKHYA_ENDPOINT": "https://yk3uyybb2e.execute-api.us-west-2.amazonaws.com/dev/customer", "CERC_ENGINE_URL": "https://cerc-engine-hml.lucree.com.br", "API_AGREEMENTS": "https://api-hml.lucree.com.br/agreements/simplified-onboarding/terms/acceptance/event", "RDSTATION_CLIENT_ID": "6ac149cf-7900-4dd1-8194-7299610589c9", "RDSTATION_CLIENT_SECRET": "********************************", "RDSTATION_BASE_PATH": "https://api.rd.services", "OCTADESK_AUTH_URL": "https://api.octadesk.services/login/apiToken", "OCTADESK_API_TOKEN": "octa.2jgLrCxh0uFa.LTQHt74VzlBj", "OCTADESK_PERSON_BASE_URL": "https://api.octadesk.services/persons"}, "exclude": ["*.md", "Dockerfile", ".*", "zappa_settings.json", "setup.cfg", "docker-*", "bitbucket-*"], "vpc_config": {"SubnetIds": ["subnet-0de4da33e830fd6e8", "subnet-074eb9ab8523354aa"], "SecurityGroupIds": ["sg-01540ffbef59b0f51"]}, "api_gateway_config": {"rest_api_id": "your_api_id", "routes": {"/schema": {"GET": {"authorizer": "NONE"}}}, "authorizer": {"type": "COGNITO_USER_POOLS", "provider_arns": ["arn:aws:cognito-idp:us-west-2:175572419266:userpool/us-west-2_axDl475s7"]}}}, "prod": {"extends": "hml", "environment_variables": {"API_DOCUMENTOS_DOWNLOAD": "https://api.lucree.com.br/documentos/download/", "API_AUTH": "https://api.lucree.com.br/autenticacao/v1", "CLIENTE_ID": "3MVG9l2zHsylwlpRWbxBy.S7N0o.XeI8dcXktzldbfQHKPuJvdpSXVehi.tWst0I5oKThbBDcji5SWSEOyyYb", "CLIENTE_SECRET": "51CAF4FBA79F52CE123A7916853C9938B0CD6924F36032852BA641B7C41A7DBD", "RDS_DB_NAME": "subadiqdb1", "RDS_USERNAME": "subadiqdb", "RDS_PASSWORD": "7rW8dBYOEbC", "RDS_HOSTNAME": "transacsubadiq.cxmg4sq9azs9.us-west-2.rds.amazonaws.com", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "CIaIYfcT70PzYIS4LCTRCnJwHT7+Bxg6LheI4+Oe", "S3_BUCKET": "swagger-static", "AWS_S3_REGION_NAME": "us-west-2", "SECRET_KEY": "sxs4ti#4^bn2md#!+)v-l9q+qv=6l2-5$79=t0j8-_#-^n+#$(", "IDWALL_ENDPOINT": "https://api-v2.idwall.co/relatorios", "IDWALL_TOKEN": "9958bcb5-9320-4dc2-b443-4e1745ca86e5", "LOG_LEVEL": "DEBUG", "SLACK_WEBHOOK_RISC_ENDPOIT": "*****************************************************************************", "API_SANKHYA_KEY": "MNaaiVIv4736inwvXUlHK8UTj0hf6o4T1YEcniRA", "API_SANKHYA_ENDPOINT": "https://6a0asfs9ud.execute-api.us-west-2.amazonaws.com/prd/customer", "CERC_ENGINE_URL": "https://cerc-engine.lucree.com.br", "API_AGREEMENTS": "https://api.lucree.com.br/agreements/simplified-onboarding/terms/acceptance/event", "RDSTATION_CLIENT_ID": "6ac149cf-7900-4dd1-8194-7299610589c9", "RDSTATION_CLIENT_SECRET": "********************************", "RDSTATION_BASE_PATH": "https://api.rd.services", "OCTADESK_AUTH_URL": "https://api.octadesk.services/login/apiToken", "OCTADESK_API_TOKEN": "octa.2jgLrCxh0uFa.LTQHt74VzlBj", "OCTADESK_PERSON_BASE_URL": "https://api.octadesk.services/persons"}, "lambda_description": "API de Credenciamento Lucree - Prod", "apigateway_description": "API de Credenciamento Lucree - Prod", "domain": "api.lucree.com.br", "endpoint_configuration": ["EDGE"], "log_level": "INFO", "keep_warm": true, "keep_warm_expression": "rate(5 minutes)", "vpc_config": {"SubnetIds": ["subnet-069b4cc8898ec7708", "subnet-01a2db17a3e4cc521", "subnet-0426a8959dc215581"], "SecurityGroupIds": ["sg-57861622"]}}}